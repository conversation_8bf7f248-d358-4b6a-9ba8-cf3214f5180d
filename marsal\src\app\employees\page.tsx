'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/auth-provider'
import { Header } from '@/components/header'
import { Navigation } from '@/components/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { getEmployees, searchEmployees, deleteEmployee } from '@/lib/storage'
import { User } from '@/types'
import { Plus, Search, Edit, Trash2, Users, Phone, Mail, Calendar } from 'lucide-react'

export default function EmployeesPage() {
  const { user, isLoading } = useAuth()
  const router = useRouter()
  const [employees, setEmployees] = useState<User[]>([])
  const [filteredEmployees, setFilteredEmployees] = useState<User[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [roleFilter, setRoleFilter] = useState<'all' | 'supervisor' | 'courier'>('all')
  const [isLoadingEmployees, setIsLoadingEmployees] = useState(true)

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/login')
      return
    }

    // التحقق من الصلاحيات - فقط المدير والمتابع يمكنهم الوصول
    if (user && user.role === 'courier') {
      router.push('/')
      return
    }

    if (user) {
      loadEmployees()
    }
  }, [user, isLoading, router])

  const loadEmployees = () => {
    setIsLoadingEmployees(true)
    try {
      let allEmployees = getEmployees()
      
      // فلترة الموظفين حسب دور المستخدم
      if (user?.role === 'supervisor') {
        // المتابع يرى فقط المندوبين
        allEmployees = allEmployees.filter(emp => emp.role === 'courier')
      } else if (user?.role === 'manager') {
        // المدير يرى المتابعين والمندوبين
        allEmployees = allEmployees.filter(emp => emp.role !== 'manager')
      }
      
      setEmployees(allEmployees)
      setFilteredEmployees(allEmployees)
    } catch (error) {
      console.error('Error loading employees:', error)
    } finally {
      setIsLoadingEmployees(false)
    }
  }

  useEffect(() => {
    let filtered = employees

    // تطبيق البحث
    if (searchQuery.trim()) {
      filtered = searchEmployees(searchQuery).filter(emp => {
        if (user?.role === 'supervisor') {
          return emp.role === 'courier'
        } else if (user?.role === 'manager') {
          return emp.role !== 'manager'
        }
        return true
      })
    }

    // تطبيق فلتر الدور
    if (roleFilter !== 'all') {
      filtered = filtered.filter(emp => emp.role === roleFilter)
    }

    setFilteredEmployees(filtered)
  }, [searchQuery, roleFilter, employees, user])

  const handleDeleteEmployee = async (employeeId: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
      try {
        const success = deleteEmployee(employeeId)
        if (success) {
          loadEmployees()
        } else {
          alert('حدث خطأ أثناء حذف الموظف')
        }
      } catch (error) {
        console.error('Error deleting employee:', error)
        alert('حدث خطأ أثناء حذف الموظف')
      }
    }
  }

  const getRoleLabel = (role: string) => {
    const labels = {
      manager: 'مدير',
      supervisor: 'متابع',
      courier: 'مندوب'
    }
    return labels[role as keyof typeof labels] || role
  }

  const getRoleColor = (role: string) => {
    const colors = {
      manager: 'bg-purple-100 text-purple-800',
      supervisor: 'bg-blue-100 text-blue-800',
      courier: 'bg-green-100 text-green-800'
    }
    return colors[role as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  if (isLoading || isLoadingEmployees) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!user || user.role === 'courier') return null

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="flex">
        <Navigation />
        
        <main className="flex-1 p-6">
          {/* العنوان والإجراءات */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">إدارة الموظفين</h1>
              <p className="text-gray-600 mt-1">
                إجمالي الموظفين: {filteredEmployees.length}
              </p>
            </div>
            
            <Button
              onClick={() => router.push('/employees/new')}
              className="flex items-center space-x-2 space-x-reverse"
            >
              <Plus className="h-4 w-4" />
              <span>موظف جديد</span>
            </Button>
          </div>

          {/* شريط البحث والفلاتر */}
          <Card className="mb-6">
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row gap-4">
                {/* البحث */}
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="البحث بالاسم أو اسم المستخدم أو رقم الهاتف..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pr-10"
                    />
                  </div>
                </div>

                {/* فلتر الدور */}
                {user.role === 'manager' && (
                  <div className="md:w-48">
                    <select
                      value={roleFilter}
                      onChange={(e) => setRoleFilter(e.target.value as 'all' | 'supervisor' | 'courier')}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="all">جميع الأدوار</option>
                      <option value="supervisor">متابع</option>
                      <option value="courier">مندوب</option>
                    </select>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* قائمة الموظفين */}
          {filteredEmployees.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="text-gray-400 mb-4">
                  <Users className="h-16 w-16 mx-auto" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا يوجد موظفين</h3>
                <p className="text-gray-600">
                  {searchQuery || roleFilter !== 'all' 
                    ? 'لم يتم العثور على موظفين يطابقون معايير البحث'
                    : 'لم يتم إضافة أي موظفين بعد'
                  }
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {filteredEmployees.map((employee) => (
                <Card key={employee.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-4 space-x-reverse mb-3">
                          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                            <Users className="h-6 w-6 text-blue-600" />
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900">
                              {employee.name}
                            </h3>
                            <p className="text-sm text-gray-600">@{employee.username}</p>
                          </div>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRoleColor(employee.role)}`}>
                            {getRoleLabel(employee.role)}
                          </span>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <Phone className="h-4 w-4" />
                            <span>{employee.phone || 'غير محدد'}</span>
                          </div>
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <Mail className="h-4 w-4" />
                            <span>{employee.email || 'غير محدد'}</span>
                          </div>
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <Calendar className="h-4 w-4" />
                            <span>
                              {employee.createdAt 
                                ? new Date(employee.createdAt).toLocaleDateString('ar-IQ')
                                : 'غير محدد'
                              }
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex space-x-2 space-x-reverse">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/employees/${employee.id}/edit`)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteEmployee(employee.id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </main>
      </div>
    </div>
  )
}
