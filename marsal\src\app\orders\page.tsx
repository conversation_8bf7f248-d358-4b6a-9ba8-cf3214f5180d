'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/auth-provider'
import { Header } from '@/components/header'
import { Navigation } from '@/components/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { getOrders, searchOrders, filterOrdersByStatus, filterOrdersByCourier, getStatusLabel } from '@/lib/storage'
import { Order, OrderStatus } from '@/types'
import { Plus, Search, Filter, Eye, Edit, Truck } from 'lucide-react'

export default function OrdersPage() {
  const { user, isLoading } = useAuth()
  const router = useRouter()
  const [orders, setOrders] = useState<Order[]>([])
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<OrderStatus | 'all'>('all')
  const [isLoadingOrders, setIsLoadingOrders] = useState(true)

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/login')
      return
    }

    if (user) {
      loadOrders()
    }
  }, [user, isLoading, router])

  const loadOrders = () => {
    setIsLoadingOrders(true)
    try {
      let allOrders = getOrders()
      
      // فلترة الطلبات حسب دور المستخدم
      if (user?.role === 'courier') {
        allOrders = filterOrdersByCourier(user.id)
      }
      
      setOrders(allOrders)
      setFilteredOrders(allOrders)
    } catch (error) {
      console.error('Error loading orders:', error)
    } finally {
      setIsLoadingOrders(false)
    }
  }

  useEffect(() => {
    let filtered = orders

    // تطبيق البحث
    if (searchQuery.trim()) {
      filtered = searchOrders(searchQuery).filter(order => {
        if (user?.role === 'courier') {
          return order.courierId === user.id
        }
        return true
      })
    }

    // تطبيق فلتر الحالة
    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === statusFilter)
    }

    setFilteredOrders(filtered)
  }, [searchQuery, statusFilter, orders, user])

  const getStatusColor = (status: OrderStatus) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      processing: 'bg-blue-100 text-blue-800',
      shipped: 'bg-purple-100 text-purple-800',
      out_for_delivery: 'bg-orange-100 text-orange-800',
      delivered: 'bg-green-100 text-green-800',
      returned: 'bg-red-100 text-red-800',
      cancelled: 'bg-gray-100 text-gray-800',
      postponed: 'bg-indigo-100 text-indigo-800',
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
  }

  if (isLoading || isLoadingOrders) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!user) return null

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="flex">
        <Navigation />
        
        <main className="flex-1 p-6">
          {/* العنوان والإجراءات */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">إدارة الطلبات</h1>
              <p className="text-gray-600 mt-1">
                إجمالي الطلبات: {filteredOrders.length}
              </p>
            </div>
            
            {(user.role === 'manager' || user.role === 'supervisor') && (
              <Button
                onClick={() => router.push('/orders/new')}
                className="flex items-center space-x-2 space-x-reverse"
              >
                <Plus className="h-4 w-4" />
                <span>طلب جديد</span>
              </Button>
            )}
          </div>

          {/* شريط البحث والفلاتر */}
          <Card className="mb-6">
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row gap-4">
                {/* البحث */}
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="البحث برقم التتبع أو اسم العميل أو رقم الهاتف..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pr-10"
                    />
                  </div>
                </div>

                {/* فلتر الحالة */}
                <div className="md:w-48">
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value as OrderStatus | 'all')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">جميع الحالات</option>
                    <option value="pending">في الانتظار</option>
                    <option value="processing">قيد المعالجة</option>
                    <option value="shipped">تم الشحن</option>
                    <option value="out_for_delivery">قيد التوصيل</option>
                    <option value="delivered">تم التسليم</option>
                    <option value="returned">راجع</option>
                    <option value="cancelled">ملغي</option>
                    <option value="postponed">مؤجل</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* قائمة الطلبات */}
          {filteredOrders.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="text-gray-400 mb-4">
                  <Truck className="h-16 w-16 mx-auto" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد طلبات</h3>
                <p className="text-gray-600">
                  {searchQuery || statusFilter !== 'all' 
                    ? 'لم يتم العثور على طلبات تطابق معايير البحث'
                    : 'لم يتم إنشاء أي طلبات بعد'
                  }
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {filteredOrders.map((order) => (
                <Card key={order.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-4 space-x-reverse mb-3">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {order.trackingNumber}
                          </h3>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                            {getStatusLabel(order.status)}
                          </span>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                          <div>
                            <p><strong>المرسل:</strong> {order.senderName}</p>
                            <p><strong>الهاتف:</strong> {order.senderPhone}</p>
                          </div>
                          <div>
                            <p><strong>المستلم:</strong> {order.recipientName}</p>
                            <p><strong>الهاتف:</strong> {order.recipientPhone}</p>
                            <p><strong>العنوان:</strong> {order.recipientAddress}</p>
                          </div>
                          <div>
                            <p><strong>المبلغ:</strong> {order.amount.toLocaleString()} د.ع</p>
                            <p><strong>تاريخ الإنشاء:</strong> {new Date(order.createdAt).toLocaleDateString('ar-IQ')}</p>
                            {order.courierName && (
                              <p><strong>المندوب:</strong> {order.courierName}</p>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex space-x-2 space-x-reverse">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/orders/${order.id}`)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        
                        {(user.role === 'manager' || user.role === 'supervisor' || 
                          (user.role === 'courier' && order.courierId === user.id)) && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.push(`/orders/${order.id}/edit`)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </main>
      </div>
    </div>
  )
}
