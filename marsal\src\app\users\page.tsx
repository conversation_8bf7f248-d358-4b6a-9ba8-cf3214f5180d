'use client'

import React, { useState, useEffect } from 'react'
import { useAuth } from '@/components/auth-provider'
import { Header } from '@/components/header'
import { Navigation } from '@/components/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Users, 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Phone, 
  Mail,
  User,
  Shield,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import { getUsers, addUser, updateUser, deleteUser, User as UserType } from '@/lib/storage'
import { validateIraqiPhone } from '@/lib/utils'

const roleLabels = {
  'manager': 'مدير',
  'supervisor': 'متابع',
  'courier': 'مندوب'
}

const roleColors = {
  'manager': 'bg-red-100 text-red-800',
  'supervisor': 'bg-blue-100 text-blue-800',
  'courier': 'bg-green-100 text-green-800'
}

export default function UsersPage() {
  const { user } = useAuth()
  const [users, setUsers] = useState<UserType[]>([])
  const [filteredUsers, setFilteredUsers] = useState<UserType[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState<string>('all')
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<UserType | null>(null)
  const [error, setError] = useState('')
  const [newUser, setNewUser] = useState({
    name: '',
    phone: '',
    email: '',
    role: 'courier' as 'manager' | 'supervisor' | 'courier',
    password: ''
  })

  useEffect(() => {
    if (user?.role !== 'manager' && user?.role !== 'supervisor') {
      return
    }
    loadUsers()
  }, [user])

  useEffect(() => {
    filterUsers()
  }, [users, searchTerm, roleFilter])

  const loadUsers = () => {
    const allUsers = getUsers()
    
    // المتابع يرى فقط المندوبين
    if (user?.role === 'supervisor') {
      setUsers(allUsers.filter(u => u.role === 'courier'))
    } else {
      // المدير يرى جميع المستخدمين
      setUsers(allUsers)
    }
  }

  const filterUsers = () => {
    let filtered = users

    if (searchTerm) {
      filtered = filtered.filter(user =>
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.phone.includes(searchTerm) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (roleFilter !== 'all') {
      filtered = filtered.filter(user => user.role === roleFilter)
    }

    setFilteredUsers(filtered)
  }

  const handleAddUser = () => {
    setError('')

    if (!newUser.name || !newUser.phone || !newUser.password) {
      setError('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    if (!validateIraqiPhone(newUser.phone)) {
      setError('رقم الهاتف غير صحيح')
      return
    }

    if (newUser.password.length < 6) {
      setError('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
      return
    }

    // التحقق من عدم تكرار رقم الهاتف
    const existingUser = users.find(u => u.phone === newUser.phone)
    if (existingUser) {
      setError('رقم الهاتف مستخدم بالفعل')
      return
    }

    // التحقق من صلاحيات إضافة المستخدم
    if (user?.role === 'supervisor' && newUser.role !== 'courier') {
      setError('المتابع يمكنه إضافة المندوبين فقط')
      return
    }

    const userToAdd: Omit<UserType, 'id'> = {
      name: newUser.name,
      phone: newUser.phone,
      email: newUser.email || '',
      role: newUser.role,
      password: newUser.password,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    addUser(userToAdd)
    loadUsers()
    setIsAddDialogOpen(false)
    resetForm()
  }

  const handleEditUser = (userToEdit: UserType) => {
    setSelectedUser(userToEdit)
    setNewUser({
      name: userToEdit.name,
      phone: userToEdit.phone,
      email: userToEdit.email,
      role: userToEdit.role,
      password: ''
    })
    setIsEditDialogOpen(true)
  }

  const handleUpdateUser = () => {
    if (!selectedUser) return
    setError('')

    if (!newUser.name || !newUser.phone) {
      setError('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    if (!validateIraqiPhone(newUser.phone)) {
      setError('رقم الهاتف غير صحيح')
      return
    }

    if (newUser.password && newUser.password.length < 6) {
      setError('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
      return
    }

    // التحقق من عدم تكرار رقم الهاتف (باستثناء المستخدم الحالي)
    const existingUser = users.find(u => u.phone === newUser.phone && u.id !== selectedUser.id)
    if (existingUser) {
      setError('رقم الهاتف مستخدم بالفعل')
      return
    }

    // التحقق من صلاحيات تعديل المستخدم
    if (user?.role === 'supervisor' && (selectedUser.role !== 'courier' || newUser.role !== 'courier')) {
      setError('المتابع يمكنه تعديل المندوبين فقط')
      return
    }

    const updatedUser: UserType = {
      ...selectedUser,
      name: newUser.name,
      phone: newUser.phone,
      email: newUser.email,
      role: newUser.role,
      password: newUser.password || selectedUser.password,
      updatedAt: new Date().toISOString()
    }

    updateUser(updatedUser)
    loadUsers()
    setIsEditDialogOpen(false)
    setSelectedUser(null)
    resetForm()
  }

  const handleDeleteUser = (userId: string) => {
    const userToDelete = users.find(u => u.id === userId)
    if (!userToDelete) return

    // التحقق من صلاحيات الحذف
    if (user?.role === 'supervisor' && userToDelete.role !== 'courier') {
      alert('المتابع يمكنه حذف المندوبين فقط')
      return
    }

    if (userToDelete.id === user?.id) {
      alert('لا يمكنك حذف حسابك الخاص')
      return
    }

    if (confirm(`هل أنت متأكد من حذف المستخدم ${userToDelete.name}؟`)) {
      deleteUser(userId)
      loadUsers()
    }
  }

  const resetForm = () => {
    setNewUser({
      name: '',
      phone: '',
      email: '',
      role: 'courier',
      password: ''
    })
    setError('')
  }

  const canEditUser = (userToEdit: UserType) => {
    if (user?.role === 'manager') return true
    if (user?.role === 'supervisor' && userToEdit.role === 'courier') return true
    return false
  }

  const canDeleteUser = (userToDelete: UserType) => {
    if (userToDelete.id === user?.id) return false
    if (user?.role === 'manager') return true
    if (user?.role === 'supervisor' && userToDelete.role === 'courier') return true
    return false
  }

  const getUserStats = () => {
    const managers = filteredUsers.filter(u => u.role === 'manager').length
    const supervisors = filteredUsers.filter(u => u.role === 'supervisor').length
    const couriers = filteredUsers.filter(u => u.role === 'courier').length
    return { managers, supervisors, couriers }
  }

  if (user?.role !== 'manager' && user?.role !== 'supervisor') {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="flex">
          <Navigation />
          <main className="flex-1 p-6">
            <Card>
              <CardContent className="p-8 text-center">
                <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">غير مصرح</h3>
                <p className="text-gray-600">هذه الصفحة متاحة للمديرين والمتابعين فقط</p>
              </CardContent>
            </Card>
          </main>
        </div>
      </div>
    )
  }

  const stats = getUserStats()

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="flex">
        <Navigation />
        
        <main className="flex-1 p-6">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">إدارة المستخدمين</h1>
            <p className="text-gray-600">إضافة وإدارة المستخدمين والصلاحيات</p>
          </div>

          {/* إحصائيات سريعة */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Users className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">إجمالي المستخدمين</p>
                    <p className="text-2xl font-bold text-gray-900">{filteredUsers.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-red-100 rounded-lg">
                    <Shield className="h-6 w-6 text-red-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">المديرين</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.managers}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <User className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">المتابعين</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.supervisors}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <User className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">المندوبين</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.couriers}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* أدوات التحكم */}
          <div className="mb-6 flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="البحث في المستخدمين..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="فلترة حسب الدور" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الأدوار</SelectItem>
                <SelectItem value="manager">مدير</SelectItem>
                <SelectItem value="supervisor">متابع</SelectItem>
                <SelectItem value="courier">مندوب</SelectItem>
              </SelectContent>
            </Select>

            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-blue-600 hover:bg-blue-700" onClick={resetForm}>
                  <Plus className="h-4 w-4 ml-2" />
                  إضافة مستخدم جديد
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>إضافة مستخدم جديد</DialogTitle>
                  <DialogDescription>
                    أدخل بيانات المستخدم الجديد
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4">
                  {error && (
                    <Alert variant="destructive">
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}

                  <div>
                    <Label htmlFor="name">الاسم الكامل *</Label>
                    <Input
                      id="name"
                      value={newUser.name}
                      onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                      placeholder="أدخل الاسم الكامل"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="phone">رقم الهاتف *</Label>
                    <Input
                      id="phone"
                      value={newUser.phone}
                      onChange={(e) => setNewUser({...newUser, phone: e.target.value})}
                      placeholder="0750 123 4567"
                      dir="ltr"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="email">البريد الإلكتروني</Label>
                    <Input
                      id="email"
                      type="email"
                      value={newUser.email}
                      onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                      placeholder="<EMAIL>"
                      dir="ltr"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="role">الدور *</Label>
                    <Select 
                      value={newUser.role} 
                      onValueChange={(value) => setNewUser({...newUser, role: value as any})}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {user?.role === 'manager' && (
                          <>
                            <SelectItem value="manager">مدير</SelectItem>
                            <SelectItem value="supervisor">متابع</SelectItem>
                          </>
                        )}
                        <SelectItem value="courier">مندوب</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="password">كلمة المرور *</Label>
                    <Input
                      id="password"
                      type="password"
                      value={newUser.password}
                      onChange={(e) => setNewUser({...newUser, password: e.target.value})}
                      placeholder="أدخل كلمة المرور"
                    />
                  </div>
                  
                  <div className="flex gap-2">
                    <Button onClick={handleAddUser} className="flex-1">
                      إضافة المستخدم
                    </Button>
                    <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                      إلغاء
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {/* قائمة المستخدمين */}
          <div className="grid gap-4">
            {filteredUsers.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">لا يوجد مستخدمين</h3>
                  <p className="text-gray-600">لم يتم العثور على أي مستخدمين تطابق معايير البحث</p>
                </CardContent>
              </Card>
            ) : (
              filteredUsers.map((userItem) => (
                <Card key={userItem.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">
                          {userItem.name}
                        </h3>
                        <p className="text-sm text-gray-600">معرف المستخدم: {userItem.id}</p>
                      </div>
                      <Badge className={roleColors[userItem.role]}>
                        {roleLabels[userItem.role]}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className="flex items-center text-sm text-gray-600">
                        <Phone className="h-4 w-4 ml-2" />
                        {userItem.phone}
                      </div>
                      {userItem.email && (
                        <div className="flex items-center text-sm text-gray-600">
                          <Mail className="h-4 w-4 ml-2" />
                          {userItem.email}
                        </div>
                      )}
                      <div className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="h-4 w-4 ml-2" />
                        نشط
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex gap-2">
                        {canEditUser(userItem) && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditUser(userItem)}
                          >
                            <Edit className="h-4 w-4 ml-1" />
                            تعديل
                          </Button>
                        )}
                      </div>
                      
                      {canDeleteUser(userItem) && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteUser(userItem.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4 ml-1" />
                          حذف
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>

          {/* نافذة تعديل المستخدم */}
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>تعديل المستخدم</DialogTitle>
                <DialogDescription>
                  تعديل بيانات المستخدم
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div>
                  <Label htmlFor="edit-name">الاسم الكامل *</Label>
                  <Input
                    id="edit-name"
                    value={newUser.name}
                    onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                    placeholder="أدخل الاسم الكامل"
                  />
                </div>
                
                <div>
                  <Label htmlFor="edit-phone">رقم الهاتف *</Label>
                  <Input
                    id="edit-phone"
                    value={newUser.phone}
                    onChange={(e) => setNewUser({...newUser, phone: e.target.value})}
                    placeholder="0750 123 4567"
                    dir="ltr"
                  />
                </div>
                
                <div>
                  <Label htmlFor="edit-email">البريد الإلكتروني</Label>
                  <Input
                    id="edit-email"
                    type="email"
                    value={newUser.email}
                    onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                    placeholder="<EMAIL>"
                    dir="ltr"
                  />
                </div>
                
                {user?.role === 'manager' && (
                  <div>
                    <Label htmlFor="edit-role">الدور *</Label>
                    <Select 
                      value={newUser.role} 
                      onValueChange={(value) => setNewUser({...newUser, role: value as any})}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="manager">مدير</SelectItem>
                        <SelectItem value="supervisor">متابع</SelectItem>
                        <SelectItem value="courier">مندوب</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
                
                <div>
                  <Label htmlFor="edit-password">كلمة المرور الجديدة</Label>
                  <Input
                    id="edit-password"
                    type="password"
                    value={newUser.password}
                    onChange={(e) => setNewUser({...newUser, password: e.target.value})}
                    placeholder="اتركها فارغة للاحتفاظ بالحالية"
                  />
                </div>
                
                <div className="flex gap-2">
                  <Button onClick={handleUpdateUser} className="flex-1">
                    حفظ التغييرات
                  </Button>
                  <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                    إلغاء
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </main>
      </div>
    </div>
  )
}
