'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/auth-provider'
import { Header } from '@/components/header'
import { Navigation } from '@/components/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { getOrders, getEmployees, assignOrderToCourier, updateOrderStatus } from '@/lib/storage'
import { Order, User } from '@/types'
import { Truck, Search, UserCheck, Package, Clock, MapPin } from 'lucide-react'

export default function DispatchPage() {
  const { user, isLoading } = useAuth()
  const router = useRouter()
  const [orders, setOrders] = useState<Order[]>([])
  const [couriers, setCouriers] = useState<User[]>([])
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedOrders, setSelectedOrders] = useState<string[]>([])
  const [isLoadingData, setIsLoadingData] = useState(true)

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/login')
      return
    }

    // التحقق من الصلاحيات - فقط المدير يمكنه الوصول
    if (user && user.role !== 'manager') {
      router.push('/')
      return
    }

    if (user) {
      loadData()
    }
  }, [user, isLoading, router])

  const loadData = () => {
    setIsLoadingData(true)
    try {
      // تحميل الطلبات غير المخصصة أو قيد المعالجة
      const allOrders = getOrders()
      const unassignedOrders = allOrders.filter(order => 
        order.status === 'pending' || 
        order.status === 'processing' || 
        (order.status === 'shipped' && !order.courierId)
      )
      
      setOrders(unassignedOrders)
      setFilteredOrders(unassignedOrders)

      // تحميل المندوبين
      const allEmployees = getEmployees()
      const couriersList = allEmployees.filter(emp => emp.role === 'courier')
      setCouriers(couriersList)
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setIsLoadingData(false)
    }
  }

  useEffect(() => {
    if (searchQuery.trim()) {
      const filtered = orders.filter(order =>
        order.trackingNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.recipientName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.recipientPhone.includes(searchQuery) ||
        order.recipientAddress.toLowerCase().includes(searchQuery.toLowerCase())
      )
      setFilteredOrders(filtered)
    } else {
      setFilteredOrders(orders)
    }
  }, [searchQuery, orders])

  const handleOrderSelection = (orderId: string) => {
    setSelectedOrders(prev => 
      prev.includes(orderId) 
        ? prev.filter(id => id !== orderId)
        : [...prev, orderId]
    )
  }

  const handleSelectAll = () => {
    if (selectedOrders.length === filteredOrders.length) {
      setSelectedOrders([])
    } else {
      setSelectedOrders(filteredOrders.map(order => order.id))
    }
  }

  const handleAssignToCourier = async (courierId: string) => {
    if (selectedOrders.length === 0) {
      alert('يرجى اختيار طلبات للتخصيص')
      return
    }

    const courier = couriers.find(c => c.id === courierId)
    if (!courier) return

    const confirmMessage = `هل أنت متأكد من تخصيص ${selectedOrders.length} طلب للمندوب ${courier.name}؟`
    
    if (window.confirm(confirmMessage)) {
      try {
        let successCount = 0
        
        for (const orderId of selectedOrders) {
          const success = assignOrderToCourier(orderId, courierId, courier.name)
          if (success) {
            // تحديث حالة الطلب إلى "تم الشحن"
            updateOrderStatus(orderId, 'shipped', `تم تخصيص الطلب للمندوب ${courier.name}`, undefined, undefined, undefined, undefined, user?.name || 'النظام')
            successCount++
          }
        }

        if (successCount > 0) {
          alert(`تم تخصيص ${successCount} طلب بنجاح`)
          setSelectedOrders([])
          loadData()
        } else {
          alert('حدث خطأ أثناء تخصيص الطلبات')
        }
      } catch (error) {
        console.error('Error assigning orders:', error)
        alert('حدث خطأ أثناء تخصيص الطلبات')
      }
    }
  }

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      processing: 'bg-blue-100 text-blue-800',
      shipped: 'bg-purple-100 text-purple-800',
    }
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  const getStatusLabel = (status: string) => {
    const labels = {
      pending: 'في الانتظار',
      processing: 'قيد المعالجة',
      shipped: 'تم الشحن',
    }
    return labels[status as keyof typeof labels] || status
  }

  if (isLoading || isLoadingData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!user || user.role !== 'manager') return null

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="flex">
        <Navigation />
        
        <main className="flex-1 p-6">
          {/* العنوان */}
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">توزيع الطلبات</h1>
            <p className="text-gray-600 mt-1">
              تخصيص الطلبات للمندوبين - إجمالي الطلبات: {filteredOrders.length}
            </p>
          </div>

          <div className="grid gap-6">
            {/* شريط البحث والإجراءات */}
            <Card>
              <CardContent className="p-4">
                <div className="flex flex-col lg:flex-row gap-4">
                  {/* البحث */}
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        placeholder="البحث برقم التتبع أو اسم العميل أو رقم الهاتف أو العنوان..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pr-10"
                      />
                    </div>
                  </div>

                  {/* إجراءات التحديد */}
                  <div className="flex items-center space-x-4 space-x-reverse">
                    <Button
                      variant="outline"
                      onClick={handleSelectAll}
                      className="flex items-center space-x-2 space-x-reverse"
                    >
                      <Package className="h-4 w-4" />
                      <span>
                        {selectedOrders.length === filteredOrders.length ? 'إلغاء التحديد' : 'تحديد الكل'}
                      </span>
                    </Button>
                    
                    {selectedOrders.length > 0 && (
                      <span className="text-sm text-gray-600">
                        تم تحديد {selectedOrders.length} طلب
                      </span>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* قائمة المندوبين */}
            {selectedOrders.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2 space-x-reverse">
                    <UserCheck className="h-5 w-5" />
                    <span>تخصيص للمندوب</span>
                  </CardTitle>
                  <CardDescription>
                    اختر المندوب لتخصيص {selectedOrders.length} طلب محدد
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {couriers.map((courier) => (
                      <Button
                        key={courier.id}
                        variant="outline"
                        onClick={() => handleAssignToCourier(courier.id)}
                        className="h-auto p-4 flex flex-col items-start space-y-2"
                      >
                        <div className="flex items-center space-x-2 space-x-reverse w-full">
                          <Truck className="h-5 w-5 text-blue-600" />
                          <span className="font-semibold">{courier.name}</span>
                        </div>
                        {courier.phone && (
                          <span className="text-sm text-gray-600">{courier.phone}</span>
                        )}
                      </Button>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* قائمة الطلبات */}
            {filteredOrders.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="text-gray-400 mb-4">
                    <Package className="h-16 w-16 mx-auto" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد طلبات للتوزيع</h3>
                  <p className="text-gray-600">
                    {searchQuery 
                      ? 'لم يتم العثور على طلبات تطابق معايير البحث'
                      : 'جميع الطلبات تم تخصيصها للمندوبين'
                    }
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid gap-4">
                {filteredOrders.map((order) => (
                  <Card 
                    key={order.id} 
                    className={`cursor-pointer transition-all ${
                      selectedOrders.includes(order.id) 
                        ? 'ring-2 ring-blue-500 bg-blue-50' 
                        : 'hover:shadow-md'
                    }`}
                    onClick={() => handleOrderSelection(order.id)}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-4 space-x-reverse">
                          <input
                            type="checkbox"
                            checked={selectedOrders.includes(order.id)}
                            onChange={() => handleOrderSelection(order.id)}
                            className="w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                          />
                          
                          <div className="flex-1">
                            <div className="flex items-center space-x-4 space-x-reverse mb-3">
                              <h3 className="text-lg font-semibold text-gray-900">
                                {order.trackingNumber}
                              </h3>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                                {getStatusLabel(order.status)}
                              </span>
                            </div>
                            
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                              <div>
                                <p><strong>المستلم:</strong> {order.recipientName}</p>
                                <p><strong>الهاتف:</strong> {order.recipientPhone}</p>
                              </div>
                              <div className="flex items-start space-x-2 space-x-reverse">
                                <MapPin className="h-4 w-4 mt-0.5 flex-shrink-0" />
                                <div>
                                  <p><strong>العنوان:</strong></p>
                                  <p className="text-xs">{order.recipientAddress}</p>
                                </div>
                              </div>
                              <div>
                                <p><strong>المبلغ:</strong> {order.amount.toLocaleString()} د.ع</p>
                                <div className="flex items-center space-x-2 space-x-reverse mt-1">
                                  <Clock className="h-4 w-4" />
                                  <span className="text-xs">
                                    {new Date(order.createdAt).toLocaleDateString('ar-IQ')}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  )
}
