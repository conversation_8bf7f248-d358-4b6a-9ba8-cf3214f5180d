'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/auth-provider'
import { Header } from '@/components/header'
import { Navigation } from '@/components/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { getOrders, getEmployees, addOrder } from '@/lib/storage'
import { Order, User } from '@/types'
import { Upload, Download, FileText, AlertCircle, CheckCircle, Users, Package } from 'lucide-react'
import { formatCurrency, generateTrackingNumber } from '@/lib/utils'

interface ImportResult {
  success: number
  errors: Array<{ row: number; error: string }>
  total: number
}

export default function ImportExportPage() {
  const { user, isLoading } = useAuth()
  const router = useRouter()
  const [isImporting, setIsImporting] = useState(false)
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const [isLoadingData, setIsLoadingData] = useState(true)

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/login')
      return
    }

    // التحقق من الصلاحيات - فقط المدير يمكنه الوصول
    if (user && user.role !== 'manager') {
      router.push('/')
      return
    }

    if (user) {
      setIsLoadingData(false)
    }
  }, [user, isLoading, router])

  const exportOrders = () => {
    const orders = getOrders()
    
    const csvData = [
      ['رقم التتبع', 'اسم المستلم', 'رقم الهاتف', 'العنوان', 'المبلغ', 'الحالة', 'المندوب', 'تاريخ الإنشاء', 'تاريخ التحديث'],
      ...orders.map(order => [
        order.trackingNumber,
        order.recipientName,
        order.recipientPhone,
        order.recipientAddress,
        order.amount.toString(),
        order.status,
        order.courierName || '',
        new Date(order.createdAt).toLocaleDateString('ar-IQ'),
        new Date(order.updatedAt).toLocaleDateString('ar-IQ')
      ])
    ]

    const csvContent = csvData.map(row => row.join(',')).join('\n')
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `طلبات-${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const exportEmployees = () => {
    const employees = getEmployees()
    
    const csvData = [
      ['الاسم', 'اسم المستخدم', 'الدور', 'رقم الهاتف', 'البريد الإلكتروني', 'العنوان', 'تاريخ الإنشاء'],
      ...employees.map(employee => [
        employee.name,
        employee.username,
        employee.role,
        employee.phone || '',
        employee.email || '',
        employee.address || '',
        new Date(employee.createdAt || '').toLocaleDateString('ar-IQ')
      ])
    ]

    const csvContent = csvData.map(row => row.join(',')).join('\n')
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `موظفين-${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const downloadTemplate = () => {
    const templateData = [
      ['اسم المستلم', 'رقم الهاتف', 'العنوان', 'المبلغ', 'ملاحظات'],
      ['أحمد محمد', '07701234567', 'بغداد - الكرادة - شارع الرئيسي', '50000', 'طلب عاجل'],
      ['فاطمة علي', '07809876543', 'البصرة - الجمعيات - قرب المسجد', '75000', '']
    ]

    const csvContent = templateData.map(row => row.join(',')).join('\n')
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', 'قالب-استيراد-طلبات.csv')
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleFileImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsImporting(true)
    setImportResult(null)

    try {
      const text = await file.text()
      const lines = text.split('\n').filter(line => line.trim())
      
      if (lines.length < 2) {
        throw new Error('الملف يجب أن يحتوي على عنوان وسطر واحد على الأقل من البيانات')
      }

      const headers = lines[0].split(',').map(h => h.trim())
      const dataLines = lines.slice(1)

      const result: ImportResult = {
        success: 0,
        errors: [],
        total: dataLines.length
      }

      for (let i = 0; i < dataLines.length; i++) {
        const rowNumber = i + 2 // +2 لأن الصف الأول هو العنوان والفهرسة تبدأ من 1
        const values = dataLines[i].split(',').map(v => v.trim())

        try {
          // التحقق من وجود البيانات المطلوبة
          if (values.length < 4) {
            throw new Error('بيانات ناقصة - يجب توفير اسم المستلم ورقم الهاتف والعنوان والمبلغ')
          }

          const [recipientName, recipientPhone, recipientAddress, amountStr, notes] = values

          if (!recipientName) {
            throw new Error('اسم المستلم مطلوب')
          }

          if (!recipientPhone) {
            throw new Error('رقم الهاتف مطلوب')
          }

          if (!recipientAddress) {
            throw new Error('العنوان مطلوب')
          }

          const amount = parseFloat(amountStr)
          if (isNaN(amount) || amount <= 0) {
            throw new Error('المبلغ يجب أن يكون رقماً صحيحاً أكبر من صفر')
          }

          // إنشاء الطلب
          const orderData = {
            trackingNumber: generateTrackingNumber(),
            recipientName: recipientName.trim(),
            recipientPhone: recipientPhone.trim(),
            recipientAddress: recipientAddress.trim(),
            amount,
            status: 'pending' as const,
            notes: notes?.trim() || undefined,
            createdBy: user?.name || 'النظام'
          }

          addOrder(orderData)
          result.success++

        } catch (error) {
          result.errors.push({
            row: rowNumber,
            error: error instanceof Error ? error.message : 'خطأ غير معروف'
          })
        }
      }

      setImportResult(result)

    } catch (error) {
      console.error('Error importing file:', error)
      setImportResult({
        success: 0,
        errors: [{ row: 0, error: error instanceof Error ? error.message : 'خطأ في قراءة الملف' }],
        total: 0
      })
    } finally {
      setIsImporting(false)
      // إعادة تعيين قيمة input
      event.target.value = ''
    }
  }

  if (isLoading || isLoadingData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!user || user.role !== 'manager') return null

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="flex">
        <Navigation />
        
        <main className="flex-1 p-6">
          {/* العنوان */}
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">الاستيراد والتصدير</h1>
            <p className="text-gray-600 mt-1">
              استيراد وتصدير البيانات بصيغة CSV
            </p>
          </div>

          <div className="grid gap-6">
            {/* تصدير البيانات */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 space-x-reverse">
                  <Download className="h-5 w-5" />
                  <span>تصدير البيانات</span>
                </CardTitle>
                <CardDescription>
                  تصدير البيانات الحالية إلى ملفات CSV
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button
                    onClick={exportOrders}
                    variant="outline"
                    className="h-auto p-6 flex flex-col items-center space-y-2"
                  >
                    <Package className="h-8 w-8 text-blue-600" />
                    <span className="font-semibold">تصدير الطلبات</span>
                    <span className="text-sm text-gray-600">تصدير جميع الطلبات مع تفاصيلها</span>
                  </Button>

                  <Button
                    onClick={exportEmployees}
                    variant="outline"
                    className="h-auto p-6 flex flex-col items-center space-y-2"
                  >
                    <Users className="h-8 w-8 text-green-600" />
                    <span className="font-semibold">تصدير الموظفين</span>
                    <span className="text-sm text-gray-600">تصدير بيانات جميع الموظفين</span>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* استيراد الطلبات */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 space-x-reverse">
                  <Upload className="h-5 w-5" />
                  <span>استيراد الطلبات</span>
                </CardTitle>
                <CardDescription>
                  استيراد طلبات جديدة من ملف CSV
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* تحميل القالب */}
                  <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <FileText className="h-6 w-6 text-blue-600" />
                      <div>
                        <p className="font-medium text-blue-900">قالب الاستيراد</p>
                        <p className="text-sm text-blue-700">حمل القالب لمعرفة التنسيق المطلوب</p>
                      </div>
                    </div>
                    <Button onClick={downloadTemplate} variant="outline" size="sm">
                      تحميل القالب
                    </Button>
                  </div>

                  {/* رفع الملف */}
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                    <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-lg font-medium text-gray-900 mb-2">اختر ملف CSV للاستيراد</p>
                    <p className="text-sm text-gray-600 mb-4">
                      يجب أن يحتوي الملف على: اسم المستلم، رقم الهاتف، العنوان، المبلغ
                    </p>
                    <input
                      type="file"
                      accept=".csv"
                      onChange={handleFileImport}
                      disabled={isImporting}
                      className="hidden"
                      id="csv-upload"
                    />
                    <label htmlFor="csv-upload">
                      <Button
                        as="span"
                        disabled={isImporting}
                        className="cursor-pointer"
                      >
                        {isImporting ? 'جاري الاستيراد...' : 'اختيار ملف'}
                      </Button>
                    </label>
                  </div>

                  {/* نتائج الاستيراد */}
                  {importResult && (
                    <div className="space-y-4">
                      {/* ملخص النتائج */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="bg-blue-50 p-4 rounded-lg">
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <FileText className="h-5 w-5 text-blue-600" />
                            <span className="font-medium text-blue-900">إجمالي السجلات</span>
                          </div>
                          <p className="text-2xl font-bold text-blue-900 mt-1">{importResult.total}</p>
                        </div>

                        <div className="bg-green-50 p-4 rounded-lg">
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <CheckCircle className="h-5 w-5 text-green-600" />
                            <span className="font-medium text-green-900">تم بنجاح</span>
                          </div>
                          <p className="text-2xl font-bold text-green-900 mt-1">{importResult.success}</p>
                        </div>

                        <div className="bg-red-50 p-4 rounded-lg">
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <AlertCircle className="h-5 w-5 text-red-600" />
                            <span className="font-medium text-red-900">أخطاء</span>
                          </div>
                          <p className="text-2xl font-bold text-red-900 mt-1">{importResult.errors.length}</p>
                        </div>
                      </div>

                      {/* تفاصيل الأخطاء */}
                      {importResult.errors.length > 0 && (
                        <Card>
                          <CardHeader>
                            <CardTitle className="text-red-900">تفاصيل الأخطاء</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-2 max-h-64 overflow-y-auto">
                              {importResult.errors.map((error, index) => (
                                <div key={index} className="flex items-start space-x-3 space-x-reverse p-3 bg-red-50 rounded">
                                  <AlertCircle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                                  <div className="flex-1">
                                    <p className="text-sm font-medium text-red-900">
                                      الصف {error.row}: {error.error}
                                    </p>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </CardContent>
                        </Card>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* تعليمات الاستيراد */}
            <Card>
              <CardHeader>
                <CardTitle>تعليمات الاستيراد</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-start space-x-2 space-x-reverse">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></span>
                    <p>يجب أن يكون الملف بصيغة CSV مع ترميز UTF-8</p>
                  </div>
                  <div className="flex items-start space-x-2 space-x-reverse">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></span>
                    <p>الصف الأول يجب أن يحتوي على عناوين الأعمدة</p>
                  </div>
                  <div className="flex items-start space-x-2 space-x-reverse">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></span>
                    <p>الأعمدة المطلوبة: اسم المستلم، رقم الهاتف، العنوان، المبلغ</p>
                  </div>
                  <div className="flex items-start space-x-2 space-x-reverse">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></span>
                    <p>عمود الملاحظات اختياري</p>
                  </div>
                  <div className="flex items-start space-x-2 space-x-reverse">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></span>
                    <p>سيتم إنشاء رقم تتبع تلقائياً لكل طلب</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  )
}
