'use client'

import React from 'react'
import { LucideIcon } from 'lucide-react'
import { Card, CardContent } from '../ui/card'
import Link from 'next/link'

interface SectionCardProps {
  title: string
  description: string
  icon: LucideIcon
  href: string
  color: 'blue' | 'green' | 'purple' | 'orange' | 'red' | 'indigo' | 'pink' | 'teal'
  count?: number
  trend?: {
    value: number
    isPositive: boolean
  }
}

const colorClasses = {
  blue: {
    gradient: 'from-blue-500 to-blue-600',
    bg: 'bg-blue-50',
    text: 'text-blue-600',
    border: 'border-blue-200',
    hover: 'hover:border-blue-300'
  },
  green: {
    gradient: 'from-green-500 to-green-600',
    bg: 'bg-green-50',
    text: 'text-green-600',
    border: 'border-green-200',
    hover: 'hover:border-green-300'
  },
  purple: {
    gradient: 'from-purple-500 to-purple-600',
    bg: 'bg-purple-50',
    text: 'text-purple-600',
    border: 'border-purple-200',
    hover: 'hover:border-purple-300'
  },
  orange: {
    gradient: 'from-orange-500 to-orange-600',
    bg: 'bg-orange-50',
    text: 'text-orange-600',
    border: 'border-orange-200',
    hover: 'hover:border-orange-300'
  },
  red: {
    gradient: 'from-red-500 to-red-600',
    bg: 'bg-red-50',
    text: 'text-red-600',
    border: 'border-red-200',
    hover: 'hover:border-red-300'
  },
  indigo: {
    gradient: 'from-indigo-500 to-indigo-600',
    bg: 'bg-indigo-50',
    text: 'text-indigo-600',
    border: 'border-indigo-200',
    hover: 'hover:border-indigo-300'
  },
  pink: {
    gradient: 'from-pink-500 to-pink-600',
    bg: 'bg-pink-50',
    text: 'text-pink-600',
    border: 'border-pink-200',
    hover: 'hover:border-pink-300'
  },
  teal: {
    gradient: 'from-teal-500 to-teal-600',
    bg: 'bg-teal-50',
    text: 'text-teal-600',
    border: 'border-teal-200',
    hover: 'hover:border-teal-300'
  }
}

export function SectionCard({ title, description, icon: Icon, href, color, count, trend }: SectionCardProps) {
  const colors = colorClasses[color]

  return (
    <Link href={href}>
      <Card className={`group cursor-pointer transition-all duration-300 hover-lift border-2 ${colors.border} ${colors.hover} ${colors.bg} hover:shadow-strong animate-fade-in`}>
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-3 space-x-reverse mb-3">
                <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${colors.gradient} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className={`text-lg font-bold ${colors.text} group-hover:text-opacity-80 transition-colors`}>
                    {title}
                  </h3>
                  {count !== undefined && (
                    <div className="flex items-center space-x-2 space-x-reverse mt-1">
                      <span className={`text-2xl font-bold ${colors.text}`}>
                        {count.toLocaleString('ar-EG')}
                      </span>
                      {trend && (
                        <span className={`text-sm font-medium ${trend.isPositive ? 'text-green-600' : 'text-red-600'}`}>
                          {trend.isPositive ? '+' : ''}{trend.value}%
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </div>
              
              <p className="text-gray-600 text-sm leading-relaxed mb-4">
                {description}
              </p>
              
              <div className={`inline-flex items-center text-sm font-medium ${colors.text} group-hover:translate-x-1 transition-transform duration-200`}>
                <span>عرض التفاصيل</span>
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
