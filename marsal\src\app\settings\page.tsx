'use client'

import React, { useState, useEffect } from 'react'
import { useAuth } from '@/components/auth-provider'
import { Header } from '@/components/header'
import { Navigation } from '@/components/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Settings, 
  User, 
  Bell, 
  Shield, 
  Database, 
  Palette,
  Globe,
  Save,
  RefreshCw,
  Download,
  Upload,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import { validateIraqiPhone } from '@/lib/utils'

interface AppSettings {
  // إعدادات عامة
  companyName: string
  companyPhone: string
  companyAddress: string
  currency: string
  language: string
  timezone: string
  
  // إعدادات الإشعارات
  emailNotifications: boolean
  smsNotifications: boolean
  pushNotifications: boolean
  notificationSound: boolean
  
  // إعدادات الأمان
  sessionTimeout: number
  passwordExpiry: number
  twoFactorAuth: boolean
  loginAttempts: number
  
  // إعدادات التطبيق
  autoAssignment: boolean
  orderPrefix: string
  defaultDeliveryFee: number
  workingHours: {
    start: string
    end: string
  }
  
  // إعدادات النسخ الاحتياطي
  autoBackup: boolean
  backupFrequency: string
  backupLocation: string
}

export default function SettingsPage() {
  const { user, updateUser } = useAuth()
  const [settings, setSettings] = useState<AppSettings>({
    companyName: 'مرسال للتوصيل',
    companyPhone: '07501234567',
    companyAddress: 'بغداد، العراق',
    currency: 'IQD',
    language: 'ar',
    timezone: 'Asia/Baghdad',
    emailNotifications: true,
    smsNotifications: true,
    pushNotifications: true,
    notificationSound: true,
    sessionTimeout: 30,
    passwordExpiry: 90,
    twoFactorAuth: false,
    loginAttempts: 3,
    autoAssignment: false,
    orderPrefix: 'ORD',
    defaultDeliveryFee: 5000,
    workingHours: {
      start: '08:00',
      end: '22:00'
    },
    autoBackup: true,
    backupFrequency: 'daily',
    backupLocation: 'local'
  })
  
  const [userProfile, setUserProfile] = useState({
    name: user?.name || '',
    phone: user?.phone || '',
    email: user?.email || '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  
  const [message, setMessage] = useState<{type: 'success' | 'error', text: string} | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = () => {
    // تحميل الإعدادات من localStorage
    const savedSettings = localStorage.getItem('app_settings')
    if (savedSettings) {
      setSettings(JSON.parse(savedSettings))
    }
  }

  const saveSettings = () => {
    setLoading(true)
    try {
      localStorage.setItem('app_settings', JSON.stringify(settings))
      setMessage({ type: 'success', text: 'تم حفظ الإعدادات بنجاح' })
    } catch (error) {
      setMessage({ type: 'error', text: 'حدث خطأ أثناء حفظ الإعدادات' })
    } finally {
      setLoading(false)
    }
  }

  const updateProfile = () => {
    setLoading(true)
    try {
      if (!userProfile.name || !userProfile.phone) {
        setMessage({ type: 'error', text: 'يرجى ملء جميع الحقول المطلوبة' })
        return
      }

      if (!validateIraqiPhone(userProfile.phone)) {
        setMessage({ type: 'error', text: 'رقم الهاتف غير صحيح' })
        return
      }

      if (userProfile.newPassword) {
        if (userProfile.newPassword.length < 6) {
          setMessage({ type: 'error', text: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' })
          return
        }

        if (userProfile.newPassword !== userProfile.confirmPassword) {
          setMessage({ type: 'error', text: 'كلمة المرور الجديدة غير متطابقة' })
          return
        }
      }

      // تحديث بيانات المستخدم
      if (user) {
        const updatedUser = {
          ...user,
          name: userProfile.name,
          phone: userProfile.phone,
          email: userProfile.email,
          password: userProfile.newPassword || user.password,
          updatedAt: new Date().toISOString()
        }
        
        updateUser(updatedUser)
        setMessage({ type: 'success', text: 'تم تحديث الملف الشخصي بنجاح' })
        
        // إعادة تعيين كلمات المرور
        setUserProfile(prev => ({
          ...prev,
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        }))
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'حدث خطأ أثناء تحديث الملف الشخصي' })
    } finally {
      setLoading(false)
    }
  }

  const exportData = () => {
    try {
      const data = {
        settings,
        exportDate: new Date().toISOString(),
        version: '1.0'
      }
      
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `marsal_settings_${new Date().toISOString().split('T')[0]}.json`
      link.click()
      URL.revokeObjectURL(url)
      
      setMessage({ type: 'success', text: 'تم تصدير الإعدادات بنجاح' })
    } catch (error) {
      setMessage({ type: 'error', text: 'حدث خطأ أثناء تصدير الإعدادات' })
    }
  }

  const importData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string)
        if (data.settings) {
          setSettings(data.settings)
          setMessage({ type: 'success', text: 'تم استيراد الإعدادات بنجاح' })
        } else {
          setMessage({ type: 'error', text: 'ملف الإعدادات غير صحيح' })
        }
      } catch (error) {
        setMessage({ type: 'error', text: 'حدث خطأ أثناء قراءة الملف' })
      }
    }
    reader.readAsText(file)
  }

  const resetSettings = () => {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
      localStorage.removeItem('app_settings')
      loadSettings()
      setMessage({ type: 'success', text: 'تم إعادة تعيين الإعدادات بنجاح' })
    }
  }

  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 5000)
      return () => clearTimeout(timer)
    }
  }, [message])

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="flex">
        <Navigation />
        
        <main className="flex-1 p-6">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">الإعدادات</h1>
            <p className="text-gray-600">إدارة إعدادات التطبيق والملف الشخصي</p>
          </div>

          {message && (
            <Alert className={`mb-6 ${message.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
              <div className="flex items-center">
                {message.type === 'success' ? (
                  <CheckCircle className="h-4 w-4 text-green-600 ml-2" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-600 ml-2" />
                )}
                <AlertDescription className={message.type === 'success' ? 'text-green-800' : 'text-red-800'}>
                  {message.text}
                </AlertDescription>
              </div>
            </Alert>
          )}

          <Tabs defaultValue="profile" className="space-y-6">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="profile" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                الملف الشخصي
              </TabsTrigger>
              <TabsTrigger value="general" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                عام
              </TabsTrigger>
              <TabsTrigger value="notifications" className="flex items-center gap-2">
                <Bell className="h-4 w-4" />
                الإشعارات
              </TabsTrigger>
              <TabsTrigger value="security" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                الأمان
              </TabsTrigger>
              <TabsTrigger value="backup" className="flex items-center gap-2">
                <Database className="h-4 w-4" />
                النسخ الاحتياطي
              </TabsTrigger>
            </TabsList>

            {/* الملف الشخصي */}
            <TabsContent value="profile">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    الملف الشخصي
                  </CardTitle>
                  <CardDescription>
                    تحديث معلوماتك الشخصية وكلمة المرور
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="name">الاسم الكامل *</Label>
                      <Input
                        id="name"
                        value={userProfile.name}
                        onChange={(e) => setUserProfile(prev => ({...prev, name: e.target.value}))}
                        placeholder="أدخل الاسم الكامل"
                      />
                    </div>

                    <div>
                      <Label htmlFor="phone">رقم الهاتف *</Label>
                      <Input
                        id="phone"
                        value={userProfile.phone}
                        onChange={(e) => setUserProfile(prev => ({...prev, phone: e.target.value}))}
                        placeholder="0750 123 4567"
                        dir="ltr"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <Label htmlFor="email">البريد الإلكتروني</Label>
                      <Input
                        id="email"
                        type="email"
                        value={userProfile.email}
                        onChange={(e) => setUserProfile(prev => ({...prev, email: e.target.value}))}
                        placeholder="<EMAIL>"
                        dir="ltr"
                      />
                    </div>
                  </div>

                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">تغيير كلمة المرور</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="currentPassword">كلمة المرور الحالية</Label>
                        <Input
                          id="currentPassword"
                          type="password"
                          value={userProfile.currentPassword}
                          onChange={(e) => setUserProfile(prev => ({...prev, currentPassword: e.target.value}))}
                          placeholder="كلمة المرور الحالية"
                        />
                      </div>

                      <div>
                        <Label htmlFor="newPassword">كلمة المرور الجديدة</Label>
                        <Input
                          id="newPassword"
                          type="password"
                          value={userProfile.newPassword}
                          onChange={(e) => setUserProfile(prev => ({...prev, newPassword: e.target.value}))}
                          placeholder="كلمة المرور الجديدة"
                        />
                      </div>

                      <div>
                        <Label htmlFor="confirmPassword">تأكيد كلمة المرور</Label>
                        <Input
                          id="confirmPassword"
                          type="password"
                          value={userProfile.confirmPassword}
                          onChange={(e) => setUserProfile(prev => ({...prev, confirmPassword: e.target.value}))}
                          placeholder="تأكيد كلمة المرور"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button onClick={updateProfile} disabled={loading}>
                      {loading ? (
                        <RefreshCw className="h-4 w-4 ml-2 animate-spin" />
                      ) : (
                        <Save className="h-4 w-4 ml-2" />
                      )}
                      حفظ التغييرات
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* الإعدادات العامة */}
            <TabsContent value="general">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    الإعدادات العامة
                  </CardTitle>
                  <CardDescription>
                    إعدادات الشركة والتطبيق العامة
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="companyName">اسم الشركة</Label>
                      <Input
                        id="companyName"
                        value={settings.companyName}
                        onChange={(e) => setSettings(prev => ({...prev, companyName: e.target.value}))}
                        placeholder="اسم الشركة"
                      />
                    </div>

                    <div>
                      <Label htmlFor="companyPhone">هاتف الشركة</Label>
                      <Input
                        id="companyPhone"
                        value={settings.companyPhone}
                        onChange={(e) => setSettings(prev => ({...prev, companyPhone: e.target.value}))}
                        placeholder="0750 123 4567"
                        dir="ltr"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <Label htmlFor="companyAddress">عنوان الشركة</Label>
                      <Textarea
                        id="companyAddress"
                        value={settings.companyAddress}
                        onChange={(e) => setSettings(prev => ({...prev, companyAddress: e.target.value}))}
                        placeholder="عنوان الشركة"
                        rows={3}
                      />
                    </div>

                    <div>
                      <Label htmlFor="currency">العملة</Label>
                      <Select value={settings.currency} onValueChange={(value) => setSettings(prev => ({...prev, currency: value}))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="IQD">دينار عراقي (IQD)</SelectItem>
                          <SelectItem value="USD">دولار أمريكي (USD)</SelectItem>
                          <SelectItem value="EUR">يورو (EUR)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="language">اللغة</Label>
                      <Select value={settings.language} onValueChange={(value) => setSettings(prev => ({...prev, language: value}))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ar">العربية</SelectItem>
                          <SelectItem value="en">English</SelectItem>
                          <SelectItem value="ku">کوردی</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="orderPrefix">بادئة رقم الطلب</Label>
                      <Input
                        id="orderPrefix"
                        value={settings.orderPrefix}
                        onChange={(e) => setSettings(prev => ({...prev, orderPrefix: e.target.value}))}
                        placeholder="ORD"
                      />
                    </div>

                    <div>
                      <Label htmlFor="defaultDeliveryFee">رسوم التوصيل الافتراضية</Label>
                      <Input
                        id="defaultDeliveryFee"
                        type="number"
                        value={settings.defaultDeliveryFee}
                        onChange={(e) => setSettings(prev => ({...prev, defaultDeliveryFee: parseInt(e.target.value) || 0}))}
                        placeholder="5000"
                      />
                    </div>

                    <div>
                      <Label htmlFor="workingStart">بداية ساعات العمل</Label>
                      <Input
                        id="workingStart"
                        type="time"
                        value={settings.workingHours.start}
                        onChange={(e) => setSettings(prev => ({...prev, workingHours: {...prev.workingHours, start: e.target.value}}))}
                      />
                    </div>

                    <div>
                      <Label htmlFor="workingEnd">نهاية ساعات العمل</Label>
                      <Input
                        id="workingEnd"
                        type="time"
                        value={settings.workingHours.end}
                        onChange={(e) => setSettings(prev => ({...prev, workingHours: {...prev.workingHours, end: e.target.value}}))}
                      />
                    </div>
                  </div>

                  <div className="border-t pt-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="autoAssignment">الإسناد التلقائي للطلبات</Label>
                        <p className="text-sm text-gray-600">إسناد الطلبات تلقائياً للمندوبين المتاحين</p>
                      </div>
                      <Switch
                        id="autoAssignment"
                        checked={settings.autoAssignment}
                        onCheckedChange={(checked) => setSettings(prev => ({...prev, autoAssignment: checked}))}
                      />
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button onClick={saveSettings} disabled={loading}>
                      {loading ? (
                        <RefreshCw className="h-4 w-4 ml-2 animate-spin" />
                      ) : (
                        <Save className="h-4 w-4 ml-2" />
                      )}
                      حفظ الإعدادات
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* إعدادات الإشعارات */}
            <TabsContent value="notifications">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="h-5 w-5" />
                    إعدادات الإشعارات
                  </CardTitle>
                  <CardDescription>
                    تخصيص إعدادات الإشعارات والتنبيهات
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="emailNotifications">إشعارات البريد الإلكتروني</Label>
                        <p className="text-sm text-gray-600">تلقي الإشعارات عبر البريد الإلكتروني</p>
                      </div>
                      <Switch
                        id="emailNotifications"
                        checked={settings.emailNotifications}
                        onCheckedChange={(checked) => setSettings(prev => ({...prev, emailNotifications: checked}))}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="smsNotifications">إشعارات الرسائل النصية</Label>
                        <p className="text-sm text-gray-600">تلقي الإشعارات عبر الرسائل النصية</p>
                      </div>
                      <Switch
                        id="smsNotifications"
                        checked={settings.smsNotifications}
                        onCheckedChange={(checked) => setSettings(prev => ({...prev, smsNotifications: checked}))}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="pushNotifications">الإشعارات الفورية</Label>
                        <p className="text-sm text-gray-600">تلقي الإشعارات الفورية في التطبيق</p>
                      </div>
                      <Switch
                        id="pushNotifications"
                        checked={settings.pushNotifications}
                        onCheckedChange={(checked) => setSettings(prev => ({...prev, pushNotifications: checked}))}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="notificationSound">صوت الإشعارات</Label>
                        <p className="text-sm text-gray-600">تشغيل صوت عند وصول إشعار جديد</p>
                      </div>
                      <Switch
                        id="notificationSound"
                        checked={settings.notificationSound}
                        onCheckedChange={(checked) => setSettings(prev => ({...prev, notificationSound: checked}))}
                      />
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button onClick={saveSettings} disabled={loading}>
                      {loading ? (
                        <RefreshCw className="h-4 w-4 ml-2 animate-spin" />
                      ) : (
                        <Save className="h-4 w-4 ml-2" />
                      )}
                      حفظ الإعدادات
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* إعدادات الأمان */}
            <TabsContent value="security">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    إعدادات الأمان
                  </CardTitle>
                  <CardDescription>
                    إدارة إعدادات الأمان وحماية الحساب
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="sessionTimeout">مهلة انتهاء الجلسة (دقيقة)</Label>
                      <Input
                        id="sessionTimeout"
                        type="number"
                        value={settings.sessionTimeout}
                        onChange={(e) => setSettings(prev => ({...prev, sessionTimeout: parseInt(e.target.value) || 30}))}
                        placeholder="30"
                      />
                    </div>

                    <div>
                      <Label htmlFor="passwordExpiry">انتهاء صلاحية كلمة المرور (يوم)</Label>
                      <Input
                        id="passwordExpiry"
                        type="number"
                        value={settings.passwordExpiry}
                        onChange={(e) => setSettings(prev => ({...prev, passwordExpiry: parseInt(e.target.value) || 90}))}
                        placeholder="90"
                      />
                    </div>

                    <div>
                      <Label htmlFor="loginAttempts">عدد محاولات تسجيل الدخول</Label>
                      <Input
                        id="loginAttempts"
                        type="number"
                        value={settings.loginAttempts}
                        onChange={(e) => setSettings(prev => ({...prev, loginAttempts: parseInt(e.target.value) || 3}))}
                        placeholder="3"
                      />
                    </div>
                  </div>

                  <div className="border-t pt-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="twoFactorAuth">المصادقة الثنائية</Label>
                        <p className="text-sm text-gray-600">تفعيل المصادقة الثنائية لحماية إضافية</p>
                      </div>
                      <Switch
                        id="twoFactorAuth"
                        checked={settings.twoFactorAuth}
                        onCheckedChange={(checked) => setSettings(prev => ({...prev, twoFactorAuth: checked}))}
                      />
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button onClick={saveSettings} disabled={loading}>
                      {loading ? (
                        <RefreshCw className="h-4 w-4 ml-2 animate-spin" />
                      ) : (
                        <Save className="h-4 w-4 ml-2" />
                      )}
                      حفظ الإعدادات
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* إعدادات النسخ الاحتياطي */}
            <TabsContent value="backup">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    النسخ الاحتياطي والاستيراد/التصدير
                  </CardTitle>
                  <CardDescription>
                    إدارة النسخ الاحتياطي واستيراد/تصدير البيانات
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="backupFrequency">تكرار النسخ الاحتياطي</Label>
                      <Select
                        value={settings.backupFrequency}
                        onValueChange={(value) => setSettings(prev => ({...prev, backupFrequency: value}))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="daily">يومياً</SelectItem>
                          <SelectItem value="weekly">أسبوعياً</SelectItem>
                          <SelectItem value="monthly">شهرياً</SelectItem>
                          <SelectItem value="manual">يدوياً فقط</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="backupLocation">موقع النسخ الاحتياطي</Label>
                      <Select
                        value={settings.backupLocation}
                        onValueChange={(value) => setSettings(prev => ({...prev, backupLocation: value}))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="local">محلي</SelectItem>
                          <SelectItem value="cloud">سحابي</SelectItem>
                          <SelectItem value="both">كلاهما</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="border-t pt-6">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <Label htmlFor="autoBackup">النسخ الاحتياطي التلقائي</Label>
                        <p className="text-sm text-gray-600">إنشاء نسخ احتياطية تلقائياً حسب الجدولة</p>
                      </div>
                      <Switch
                        id="autoBackup"
                        checked={settings.autoBackup}
                        onCheckedChange={(checked) => setSettings(prev => ({...prev, autoBackup: checked}))}
                      />
                    </div>
                  </div>

                  <div className="border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">استيراد وتصدير البيانات</h3>
                    <div className="flex flex-wrap gap-4">
                      <Button onClick={exportData} variant="outline">
                        <Download className="h-4 w-4 ml-2" />
                        تصدير الإعدادات
                      </Button>

                      <div>
                        <input
                          type="file"
                          accept=".json"
                          onChange={importData}
                          className="hidden"
                          id="import-settings"
                        />
                        <Button asChild variant="outline">
                          <label htmlFor="import-settings" className="cursor-pointer">
                            <Upload className="h-4 w-4 ml-2" />
                            استيراد الإعدادات
                          </label>
                        </Button>
                      </div>

                      <Button onClick={resetSettings} variant="destructive">
                        <RefreshCw className="h-4 w-4 ml-2" />
                        إعادة تعيين الإعدادات
                      </Button>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button onClick={saveSettings} disabled={loading}>
                      {loading ? (
                        <RefreshCw className="h-4 w-4 ml-2 animate-spin" />
                      ) : (
                        <Save className="h-4 w-4 ml-2" />
                      )}
                      حفظ الإعدادات
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  )
}
