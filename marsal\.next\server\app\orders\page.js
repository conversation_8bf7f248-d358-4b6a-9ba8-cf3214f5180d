/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/orders/page";
exports.ids = ["app/orders/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Forders%2Fpage&page=%2Forders%2Fpage&appPaths=%2Forders%2Fpage&pagePath=private-next-app-dir%2Forders%2Fpage.tsx&appDir=E%3A%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5Cmarsal%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5Cmarsal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Forders%2Fpage&page=%2Forders%2Fpage&appPaths=%2Forders%2Fpage&pagePath=private-next-app-dir%2Forders%2Fpage.tsx&appDir=E%3A%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5Cmarsal%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5Cmarsal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/orders/page.tsx */ \"(rsc)/./src/app/orders/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'orders',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/orders/page\",\n        pathname: \"/orders\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Forders%2Fpage&page=%2Forders%2Fpage&appPaths=%2Forders%2Fpage&pagePath=private-next-app-dir%2Forders%2Fpage.tsx&appDir=E%3A%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5Cmarsal%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5Cmarsal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth-provider.tsx */ \"(rsc)/./src/components/auth-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRDklODUlRDklODYlRDglQUYlRDklODglRDglQTglNUMlNUNtYXJzYWwlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDJUQ5JTg1JUQ5JTg2JUQ4JUFGJUQ5JTg4JUQ4JUE4JTVDJTVDbWFyc2FsJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q2F1dGgtcHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBNEgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIkU6XFxcXNmF2YbYr9mI2KhcXFxcbWFyc2FsXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGF1dGgtcHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Corders%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Corders%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/orders/page.tsx */ \"(rsc)/./src/app/orders/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRDklODUlRDklODYlRDglQUYlRDklODglRDglQTglNUMlNUNtYXJzYWwlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNvcmRlcnMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQWtGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFzZhdmG2K/ZiNioXFxcXG1hcnNhbFxcXFxzcmNcXFxcYXBwXFxcXG9yZGVyc1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Corders%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkU6XFzZhdmG2K/ZiNioXFxtYXJzYWxcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c9dd9693e7da\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJFOlxc2YXZhtiv2YjYqFxcbWFyc2FsXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJjOWRkOTY5M2U3ZGFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_auth_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth-provider */ \"(rsc)/./src/components/auth-provider.tsx\");\n\n\n\nconst metadata = {\n    title: \"مرسال - نظام إدارة التوصيل\",\n    description: \"نظام إدارة التوصيل الكامل باللغة العربية\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"antialiased\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_provider__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUN1QjtBQUNtQztBQUVuRCxNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO1FBQUtDLEtBQUk7a0JBQ2xCLDRFQUFDQztZQUFLQyxXQUFVO3NCQUNkLDRFQUFDVixtRUFBWUE7MEJBQ1ZLOzs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIkU6XFzZhdmG2K/ZiNioXFxtYXJzYWxcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSBcIkAvY29tcG9uZW50cy9hdXRoLXByb3ZpZGVyXCI7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcItmF2LHYs9in2YQgLSDZhti42KfZhSDYpdiv2KfYsdipINin2YTYqtmI2LXZitmEXCIsXG4gIGRlc2NyaXB0aW9uOiBcItmG2LjYp9mFINil2K/Yp9ix2Kkg2KfZhNiq2YjYtdmK2YQg2KfZhNmD2KfZhdmEINio2KfZhNmE2LrYqSDYp9mE2LnYsdio2YrYqVwiLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiYXJcIiBkaXI9XCJydGxcIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT1cImFudGlhbGlhc2VkXCI+XG4gICAgICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L0F1dGhQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiQXV0aFByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJkaXIiLCJib2R5IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/orders/page.tsx":
/*!*********************************!*\
  !*** ./src/app/orders/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\مندوب\\marsal\\src\\app\\orders\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/auth-provider.tsx":
/*!******************************************!*\
  !*** ./src/components/auth-provider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\مندوب\\marsal\\src\\components\\auth-provider.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\مندوب\\marsal\\src\\components\\auth-provider.tsx",
"useAuth",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/auth-provider.tsx */ \"(ssr)/./src/components/auth-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRDklODUlRDklODYlRDglQUYlRDklODglRDglQTglNUMlNUNtYXJzYWwlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDJUQ5JTg1JUQ5JTg2JUQ4JUFGJUQ5JTg4JUQ4JUE4JTVDJTVDbWFyc2FsJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q2F1dGgtcHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBNEgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhQcm92aWRlclwiXSAqLyBcIkU6XFxcXNmF2YbYr9mI2KhcXFxcbWFyc2FsXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGF1dGgtcHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Csrc%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Corders%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Corders%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/orders/page.tsx */ \"(ssr)/./src/app/orders/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRDklODUlRDklODYlRDglQUYlRDklODglRDglQTglNUMlNUNtYXJzYWwlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNvcmRlcnMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQWtGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFzZhdmG2K/ZiNioXFxcXG1hcnNhbFxcXFxzcmNcXFxcYXBwXFxcXG9yZGVyc1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5C%5Cmarsal%5C%5Csrc%5C%5Capp%5C%5Corders%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/orders/page.tsx":
/*!*********************************!*\
  !*** ./src/app/orders/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrdersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_auth_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/auth-provider */ \"(ssr)/./src/components/auth-provider.tsx\");\n/* harmony import */ var _components_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/header */ \"(ssr)/./src/components/header.tsx\");\n/* harmony import */ var _components_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/navigation */ \"(ssr)/./src/components/navigation.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_storage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/storage */ \"(ssr)/./src/lib/storage.ts\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Plus_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Plus,Search,Truck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Plus_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Plus,Search,Truck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Plus_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Plus,Search,Truck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Plus_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Plus,Search,Truck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Plus_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Plus,Search,Truck!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction OrdersPage() {\n    const { user, isLoading } = (0,_components_auth_provider__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [orders, setOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredOrders, setFilteredOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [isLoadingOrders, setIsLoadingOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrdersPage.useEffect\": ()=>{\n            if (!isLoading && !user) {\n                router.push('/login');\n                return;\n            }\n            if (user) {\n                loadOrders();\n            }\n        }\n    }[\"OrdersPage.useEffect\"], [\n        user,\n        isLoading,\n        router\n    ]);\n    const loadOrders = ()=>{\n        setIsLoadingOrders(true);\n        try {\n            let allOrders = (0,_lib_storage__WEBPACK_IMPORTED_MODULE_9__.getOrders)();\n            // فلترة الطلبات حسب دور المستخدم\n            if (user?.role === 'courier') {\n                allOrders = (0,_lib_storage__WEBPACK_IMPORTED_MODULE_9__.filterOrdersByCourier)(user.id);\n            }\n            setOrders(allOrders);\n            setFilteredOrders(allOrders);\n        } catch (error) {\n            console.error('Error loading orders:', error);\n        } finally{\n            setIsLoadingOrders(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrdersPage.useEffect\": ()=>{\n            let filtered = orders;\n            // تطبيق البحث\n            if (searchQuery.trim()) {\n                filtered = (0,_lib_storage__WEBPACK_IMPORTED_MODULE_9__.searchOrders)(searchQuery).filter({\n                    \"OrdersPage.useEffect\": (order)=>{\n                        if (user?.role === 'courier') {\n                            return order.courierId === user.id;\n                        }\n                        return true;\n                    }\n                }[\"OrdersPage.useEffect\"]);\n            }\n            // تطبيق فلتر الحالة\n            if (statusFilter !== 'all') {\n                filtered = filtered.filter({\n                    \"OrdersPage.useEffect\": (order)=>order.status === statusFilter\n                }[\"OrdersPage.useEffect\"]);\n            }\n            setFilteredOrders(filtered);\n        }\n    }[\"OrdersPage.useEffect\"], [\n        searchQuery,\n        statusFilter,\n        orders,\n        user\n    ]);\n    const getStatusColor = (status)=>{\n        const colors = {\n            pending: 'bg-yellow-100 text-yellow-800',\n            processing: 'bg-blue-100 text-blue-800',\n            shipped: 'bg-purple-100 text-purple-800',\n            out_for_delivery: 'bg-orange-100 text-orange-800',\n            delivered: 'bg-green-100 text-green-800',\n            returned: 'bg-red-100 text-red-800',\n            cancelled: 'bg-gray-100 text-gray-800',\n            postponed: 'bg-indigo-100 text-indigo-800'\n        };\n        return colors[status] || 'bg-gray-100 text-gray-800';\n    };\n    if (isLoading || isLoadingOrders) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"جاري التحميل...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header__WEBPACK_IMPORTED_MODULE_4__.Header, {}, void 0, false, {\n                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation__WEBPACK_IMPORTED_MODULE_5__.Navigation, {}, void 0, false, {\n                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-gray-900\",\n                                                children: \"إدارة الطلبات\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mt-1\",\n                                                children: [\n                                                    \"إجمالي الطلبات: \",\n                                                    filteredOrders.length\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this),\n                                    (user.role === 'manager' || user.role === 'supervisor') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: ()=>router.push('/orders/new'),\n                                        className: \"flex items-center space-x-2 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"طلب جديد\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col md:flex-row gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                            placeholder: \"البحث برقم التتبع أو اسم العميل أو رقم الهاتف...\",\n                                                            value: searchQuery,\n                                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                                            className: \"pr-10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"md:w-48\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: statusFilter,\n                                                    onChange: (e)=>setStatusFilter(e.target.value),\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"all\",\n                                                            children: \"جميع الحالات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"pending\",\n                                                            children: \"في الانتظار\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"processing\",\n                                                            children: \"قيد المعالجة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"shipped\",\n                                                            children: \"تم الشحن\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"out_for_delivery\",\n                                                            children: \"قيد التوصيل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"delivered\",\n                                                            children: \"تم التسليم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"returned\",\n                                                            children: \"راجع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"cancelled\",\n                                                            children: \"ملغي\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"postponed\",\n                                                            children: \"مؤجل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            filteredOrders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400 mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-16 w-16 mx-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"لا توجد طلبات\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: searchQuery || statusFilter !== 'all' ? 'لم يتم العثور على طلبات تطابق معايير البحث' : 'لم يتم إنشاء أي طلبات بعد'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: filteredOrders.map((order)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                        className: \"hover:shadow-md transition-shadow\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                            className: \"p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-4 space-x-reverse mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                                        children: order.trackingNumber\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 193,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: `px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`,\n                                                                        children: (0,_lib_storage__WEBPACK_IMPORTED_MODULE_9__.getStatusLabel)(order.status)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 196,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                        children: \"المرسل:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                                        lineNumber: 203,\n                                                                                        columnNumber: 32\n                                                                                    }, this),\n                                                                                    \" \",\n                                                                                    order.senderName\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 203,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                        children: \"الهاتف:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                                        lineNumber: 204,\n                                                                                        columnNumber: 32\n                                                                                    }, this),\n                                                                                    \" \",\n                                                                                    order.senderPhone\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 204,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 202,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                        children: \"المستلم:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                                        lineNumber: 207,\n                                                                                        columnNumber: 32\n                                                                                    }, this),\n                                                                                    \" \",\n                                                                                    order.recipientName\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 207,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                        children: \"الهاتف:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                                        lineNumber: 208,\n                                                                                        columnNumber: 32\n                                                                                    }, this),\n                                                                                    \" \",\n                                                                                    order.recipientPhone\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 208,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                        children: \"العنوان:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                                        lineNumber: 209,\n                                                                                        columnNumber: 32\n                                                                                    }, this),\n                                                                                    \" \",\n                                                                                    order.recipientAddress\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 209,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 206,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                        children: \"المبلغ:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                                        lineNumber: 212,\n                                                                                        columnNumber: 32\n                                                                                    }, this),\n                                                                                    \" \",\n                                                                                    order.amount.toLocaleString(),\n                                                                                    \" د.ع\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 212,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                        children: \"تاريخ الإنشاء:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                                        lineNumber: 213,\n                                                                                        columnNumber: 32\n                                                                                    }, this),\n                                                                                    \" \",\n                                                                                    new Date(order.createdAt).toLocaleDateString('ar-IQ')\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 213,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            order.courierName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                        children: \"المندوب:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                                        lineNumber: 215,\n                                                                                        columnNumber: 34\n                                                                                    }, this),\n                                                                                    \" \",\n                                                                                    order.courierName\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                                lineNumber: 215,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                        lineNumber: 211,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2 space-x-reverse\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>router.push(`/orders/${order.id}`),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            (user.role === 'manager' || user.role === 'supervisor' || user.role === 'courier' && order.courierId === user.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>router.push(`/orders/${order.id}/edit`),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Plus_Search_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                    lineNumber: 237,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, order.id, false, {\n                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\app\\\\orders\\\\page.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/orders/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth-provider.tsx":
/*!******************************************!*\
  !*** ./src/components/auth-provider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // تهيئة المستخدمين الافتراضيين\n            (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.initializeDefaultUsers)();\n            // التحقق من الجلسة الحالية\n            const currentUser = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getCurrentUser)();\n            setUser(currentUser);\n            setIsLoading(false);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = (user)=>{\n        setUser(user);\n    };\n    const logout = ()=>{\n        setUser(null);\n        if (false) {}\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            isLoading,\n            login,\n            logout\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\auth-provider.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/header.tsx":
/*!***********************************!*\
  !*** ./src/components/header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _auth_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./auth-provider */ \"(ssr)/./src/components/auth-provider.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_LogOut_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,LogOut,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_LogOut_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,LogOut,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_LogOut_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,LogOut,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_LogOut_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,LogOut,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\nfunction Header() {\n    const { user, logout } = (0,_auth_provider__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const handleLogout = ()=>{\n        logout();\n        window.location.href = '/login';\n    };\n    const getRoleColor = (role)=>{\n        switch(role){\n            case 'manager':\n                return 'bg-gradient-to-r from-purple-500 to-purple-600 text-white';\n            case 'supervisor':\n                return 'bg-gradient-to-r from-blue-500 to-blue-600 text-white';\n            case 'courier':\n                return 'bg-gradient-to-r from-green-500 to-green-600 text-white';\n            default:\n                return 'bg-gray-100 text-gray-700';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"glass-effect border-b border-white/20 sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3 space-x-reverse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 gradient-primary rounded-xl flex items-center justify-center shadow-lg animate-pulse-slow\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white font-bold text-lg\",\n                                    children: \"م\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gradient\",\n                                        children: \"مرسال\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 -mt-1\",\n                                        children: \"نظام إدارة التوصيل المتطور\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 max-w-lg mx-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"بحث سريع برقم التتبع أو اسم العميل...\",\n                                    className: \"w-full pr-10 pl-4 py-2 bg-white/50 rounded-full border border-white/20 backdrop-blur-sm text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4 space-x-reverse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: user?.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                                                        lineNumber: 72,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: [\n                                                            user?.role === 'manager' && 'مدير',\n                                                            user?.role === 'supervisor' && 'متابع',\n                                                            user?.role === 'courier' && 'مندوب'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        onClick: handleLogout,\n                                        className: \"text-gray-400 hover:text-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_LogOut_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\header.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navigation: () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _auth_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./auth-provider */ \"(ssr)/./src/components/auth-provider.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Download,Home,Package,RotateCcw,Settings,Truck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Download,Home,Package,RotateCcw,Settings,Truck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Download,Home,Package,RotateCcw,Settings,Truck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Download,Home,Package,RotateCcw,Settings,Truck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Download,Home,Package,RotateCcw,Settings,Truck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Download,Home,Package,RotateCcw,Settings,Truck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Download,Home,Package,RotateCcw,Settings,Truck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Download,Home,Package,RotateCcw,Settings,Truck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Download,Home,Package,RotateCcw,Settings,Truck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Download,Home,Package,RotateCcw,Settings,Truck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,BarChart3,Bell,Calculator,Download,Home,Package,RotateCcw,Settings,Truck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ Navigation auto */ \n\n\n\n\n\n\nconst navigationItems = [\n    {\n        name: 'الرئيسية',\n        href: '/',\n        icon: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        roles: [\n            'manager',\n            'supervisor',\n            'courier'\n        ]\n    },\n    {\n        name: 'إدارة الطلبات',\n        href: '/orders',\n        icon: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        roles: [\n            'manager',\n            'supervisor',\n            'courier'\n        ]\n    },\n    {\n        name: 'إسناد الطلبات',\n        href: '/dispatch',\n        icon: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        roles: [\n            'manager'\n        ]\n    },\n    {\n        name: 'إدارة الرواجع',\n        href: '/returns',\n        icon: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        roles: [\n            'manager'\n        ]\n    },\n    {\n        name: 'المحاسبة',\n        href: '/accounting',\n        icon: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        roles: [\n            'manager'\n        ]\n    },\n    {\n        name: 'الإحصائيات',\n        href: '/statistics',\n        icon: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        roles: [\n            'manager',\n            'supervisor',\n            'courier'\n        ]\n    },\n    {\n        name: 'الأرشيف',\n        href: '/archive',\n        icon: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        roles: [\n            'manager',\n            'supervisor',\n            'courier'\n        ]\n    },\n    {\n        name: 'إدارة الموظفين',\n        href: '/users',\n        icon: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        roles: [\n            'manager',\n            'supervisor'\n        ]\n    },\n    {\n        name: 'استيراد وتصدير',\n        href: '/import-export',\n        icon: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        roles: [\n            'manager'\n        ]\n    },\n    {\n        name: 'الإشعارات',\n        href: '/notifications',\n        icon: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        roles: [\n            'manager',\n            'supervisor',\n            'courier'\n        ]\n    },\n    {\n        name: 'الإعدادات',\n        href: '/settings',\n        icon: _barrel_optimize_names_Archive_BarChart3_Bell_Calculator_Download_Home_Package_RotateCcw_Settings_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        roles: [\n            'manager',\n            'supervisor',\n            'courier'\n        ]\n    }\n];\nfunction Navigation() {\n    const { user } = (0,_auth_provider__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    if (!user) return null;\n    // فلترة العناصر حسب دور المستخدم\n    const filteredItems = navigationItems.filter((item)=>item.roles.includes(user.role));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white border-r border-gray-200 w-64 min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"space-y-2\",\n                children: filteredItems.map((item)=>{\n                    const Icon = item.icon;\n                    const isActive = pathname === item.href;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: item.href,\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)('flex items-center space-x-3 space-x-reverse px-3 py-2 rounded-md text-sm font-medium transition-colors', isActive ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: item.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\navigation.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\navigation.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 17\n                        }, this)\n                    }, item.name, false, {\n                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\navigation.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 15\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\navigation.tsx\",\n                lineNumber: 112,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\navigation.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\navigation.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBS2hDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCxnV0FDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsiRTpcXNmF2YbYr9mI2KhcXG1hcnNhbFxcc3JjXFxjb21wb25lbnRzXFx1aVxcaW5wdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuZXhwb3J0IGludGVyZmFjZSBJbnB1dFByb3BzXG4gIGV4dGVuZHMgUmVhY3QuSW5wdXRIVE1MQXR0cmlidXRlczxIVE1MSW5wdXRFbGVtZW50PiB7fVxuXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgSW5wdXRQcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxpbnB1dFxuICAgICAgICB0eXBlPXt0eXBlfVxuICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgIFwiZmxleCBoLTEwIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1zbSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIGZpbGU6Ym9yZGVyLTAgZmlsZTpiZy10cmFuc3BhcmVudCBmaWxlOnRleHQtc20gZmlsZTpmb250LW1lZGl1bSBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXG4gICAgICAgICAgY2xhc3NOYW1lXG4gICAgICAgICl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuSW5wdXQuZGlzcGxheU5hbWUgPSBcIklucHV0XCJcblxuZXhwb3J0IHsgSW5wdXQgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJJbnB1dCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJyZWYiLCJpbnB1dCIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addUser: () => (/* binding */ addUser),\n/* harmony export */   deleteUser: () => (/* binding */ deleteUser),\n/* harmony export */   getCouriers: () => (/* binding */ getCouriers),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getUsers: () => (/* binding */ getUsers),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   hasRole: () => (/* binding */ hasRole),\n/* harmony export */   initializeDefaultUsers: () => (/* binding */ initializeDefaultUsers),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   login: () => (/* binding */ login),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   updateUser: () => (/* binding */ updateUser)\n/* harmony export */ });\n// المستخدمون الافتراضيون\nconst defaultUsers = [\n    {\n        id: '1',\n        name: 'مدير النظام',\n        username: 'manager',\n        password: '123456',\n        phone: '07901234567',\n        role: 'manager',\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    },\n    {\n        id: '2',\n        name: 'المتابع الأول',\n        username: 'supervisor',\n        password: '123456',\n        phone: '07901234568',\n        role: 'supervisor',\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    },\n    {\n        id: '3',\n        name: 'المندوب الأول',\n        username: 'courier',\n        password: '123456',\n        phone: '07901234569',\n        role: 'courier',\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    }\n];\n// تهيئة المستخدمين الافتراضيين\nfunction initializeDefaultUsers() {\n    if (true) return;\n    const existingUsers = localStorage.getItem('marsal_users');\n    if (!existingUsers) {\n        localStorage.setItem('marsal_users', JSON.stringify(defaultUsers));\n    }\n}\n// تسجيل الدخول\nfunction login(username, password) {\n    if (true) return null;\n    const users = getUsers();\n    const user = users.find((u)=>u.username === username && u.password === password && u.isActive);\n    if (user) {\n        // حفظ بيانات الجلسة\n        const session = {\n            userId: user.id,\n            username: user.username,\n            role: user.role,\n            loginTime: new Date().toISOString(),\n            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()\n        };\n        localStorage.setItem('marsal_session', JSON.stringify(session));\n        return user;\n    }\n    return null;\n}\n// تسجيل الخروج\nfunction logout() {\n    if (true) return;\n    localStorage.removeItem('marsal_session');\n}\n// التحقق من الجلسة\nfunction getCurrentUser() {\n    if (true) return null;\n    const sessionData = localStorage.getItem('marsal_session');\n    if (!sessionData) return null;\n    try {\n        const session = JSON.parse(sessionData);\n        const now = new Date();\n        const expiresAt = new Date(session.expiresAt);\n        if (now > expiresAt) {\n            logout();\n            return null;\n        }\n        const users = getUsers();\n        const user = users.find((u)=>u.id === session.userId && u.isActive);\n        return user || null;\n    } catch  {\n        logout();\n        return null;\n    }\n}\n// التحقق من صحة الجلسة\nfunction isAuthenticated() {\n    return getCurrentUser() !== null;\n}\n// التحقق من الدور\nfunction hasRole(role) {\n    const user = getCurrentUser();\n    return user?.role === role;\n}\n// التحقق من الصلاحية\nfunction hasPermission(permission) {\n    const user = getCurrentUser();\n    if (!user) return false;\n    // المدير له جميع الصلاحيات\n    if (user.role === 'manager') return true;\n    // تحديد الصلاحيات حسب الدور\n    const permissions = {\n        manager: [\n            '*'\n        ],\n        supervisor: [\n            'view_orders',\n            'create_orders',\n            'edit_orders',\n            'view_statistics',\n            'manage_users',\n            'view_archive',\n            'manage_notifications',\n            'manage_settings',\n            'return_delivered_orders'\n        ],\n        courier: [\n            'view_orders',\n            'create_orders',\n            'edit_orders',\n            'view_statistics',\n            'view_archive_own',\n            'manage_notifications',\n            'manage_settings'\n        ]\n    };\n    const userPermissions = permissions[user.role] || [];\n    return userPermissions.includes('*') || userPermissions.includes(permission);\n}\n// الحصول على جميع المستخدمين\nfunction getUsers() {\n    if (true) return [];\n    const usersData = localStorage.getItem('marsal_users');\n    if (!usersData) {\n        initializeDefaultUsers();\n        return defaultUsers;\n    }\n    try {\n        return JSON.parse(usersData);\n    } catch  {\n        return defaultUsers;\n    }\n}\n// إضافة مستخدم جديد\nfunction addUser(userData) {\n    if (true) return null;\n    const users = getUsers();\n    // التحقق من تكرار اسم المستخدم\n    if (users.some((u)=>u.username === userData.username)) {\n        return null;\n    }\n    const newUser = {\n        ...userData,\n        id: Date.now().toString(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    };\n    users.push(newUser);\n    localStorage.setItem('marsal_users', JSON.stringify(users));\n    return newUser;\n}\n// تحديث مستخدم\nfunction updateUser(userId, updates) {\n    if (true) return false;\n    const users = getUsers();\n    const userIndex = users.findIndex((u)=>u.id === userId);\n    if (userIndex === -1) return false;\n    users[userIndex] = {\n        ...users[userIndex],\n        ...updates,\n        updatedAt: new Date().toISOString()\n    };\n    localStorage.setItem('marsal_users', JSON.stringify(users));\n    return true;\n}\n// حذف مستخدم\nfunction deleteUser(userId) {\n    if (true) return false;\n    const users = getUsers();\n    const filteredUsers = users.filter((u)=>u.id !== userId);\n    if (filteredUsers.length === users.length) return false;\n    localStorage.setItem('marsal_users', JSON.stringify(filteredUsers));\n    return true;\n}\n// الحصول على المندوبين فقط\nfunction getCouriers() {\n    return getUsers().filter((u)=>u.role === 'courier' && u.isActive);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/storage.ts":
/*!****************************!*\
  !*** ./src/lib/storage.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addEmployee: () => (/* binding */ addEmployee),\n/* harmony export */   addNotification: () => (/* binding */ addNotification),\n/* harmony export */   addOrder: () => (/* binding */ addOrder),\n/* harmony export */   assignOrderToCourier: () => (/* binding */ assignOrderToCourier),\n/* harmony export */   assignOrdersToCourier: () => (/* binding */ assignOrdersToCourier),\n/* harmony export */   calculateStatistics: () => (/* binding */ calculateStatistics),\n/* harmony export */   deleteEmployee: () => (/* binding */ deleteEmployee),\n/* harmony export */   deleteOrder: () => (/* binding */ deleteOrder),\n/* harmony export */   filterOrdersByCourier: () => (/* binding */ filterOrdersByCourier),\n/* harmony export */   filterOrdersByStatus: () => (/* binding */ filterOrdersByStatus),\n/* harmony export */   getEmployees: () => (/* binding */ getEmployees),\n/* harmony export */   getNotifications: () => (/* binding */ getNotifications),\n/* harmony export */   getOrders: () => (/* binding */ getOrders),\n/* harmony export */   getStatusLabel: () => (/* binding */ getStatusLabel),\n/* harmony export */   markNotificationAsRead: () => (/* binding */ markNotificationAsRead),\n/* harmony export */   searchEmployees: () => (/* binding */ searchEmployees),\n/* harmony export */   searchOrders: () => (/* binding */ searchOrders),\n/* harmony export */   updateEmployee: () => (/* binding */ updateEmployee),\n/* harmony export */   updateOrder: () => (/* binding */ updateOrder),\n/* harmony export */   updateOrderStatus: () => (/* binding */ updateOrderStatus)\n/* harmony export */ });\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils */ \"(ssr)/./src/lib/utils.ts\");\n\n// مفاتيح التخزين المحلي\nconst STORAGE_KEYS = {\n    ORDERS: 'marsal_orders',\n    NOTIFICATIONS: 'marsal_notifications',\n    SETTINGS: 'marsal_settings'\n};\n// الحصول على جميع الطلبات\nfunction getOrders() {\n    if (true) return [];\n    const ordersData = localStorage.getItem(STORAGE_KEYS.ORDERS);\n    if (!ordersData) return [];\n    try {\n        return JSON.parse(ordersData);\n    } catch  {\n        return [];\n    }\n}\n// إضافة طلب جديد\nfunction addOrder(orderData) {\n    const orders = getOrders();\n    const newOrder = {\n        ...orderData,\n        id: Date.now().toString(),\n        trackingNumber: (0,_utils__WEBPACK_IMPORTED_MODULE_0__.generateTrackingNumber)(),\n        statusHistory: [\n            {\n                id: Date.now().toString(),\n                orderId: '',\n                status: orderData.status,\n                notes: 'تم إنشاء الطلب',\n                updatedBy: 'النظام',\n                updatedAt: new Date().toISOString()\n            }\n        ],\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    };\n    // تحديث معرف الطلب في تاريخ الحالة\n    newOrder.statusHistory[0].orderId = newOrder.id;\n    orders.push(newOrder);\n    localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(orders));\n    return newOrder;\n}\n// تحديث طلب\nfunction updateOrder(orderId, updates) {\n    const orders = getOrders();\n    const orderIndex = orders.findIndex((o)=>o.id === orderId);\n    if (orderIndex === -1) return false;\n    orders[orderIndex] = {\n        ...orders[orderIndex],\n        ...updates,\n        updatedAt: new Date().toISOString()\n    };\n    localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(orders));\n    return true;\n}\n// تحديث حالة الطلب\nfunction updateOrderStatus(orderId, status, notes, image, returnReason, returnedPieces, newAmount, updatedBy = 'المستخدم') {\n    const orders = getOrders();\n    const orderIndex = orders.findIndex((o)=>o.id === orderId);\n    if (orderIndex === -1) return false;\n    const order = orders[orderIndex];\n    // إنشاء تحديث جديد للحالة\n    const statusUpdate = {\n        id: Date.now().toString(),\n        orderId,\n        status,\n        notes,\n        image,\n        returnReason: returnReason,\n        returnedPieces,\n        newAmount,\n        updatedBy,\n        updatedAt: new Date().toISOString()\n    };\n    // تحديث الطلب\n    order.status = status;\n    order.statusHistory.push(statusUpdate);\n    order.updatedAt = new Date().toISOString();\n    // تحديث البيانات الإضافية حسب الحالة\n    if (returnReason) order.returnReason = returnReason;\n    if (returnedPieces !== undefined) order.returnedPieces = returnedPieces;\n    if (newAmount !== undefined) {\n        order.originalAmount = order.originalAmount || order.amount;\n        order.amount = newAmount;\n    }\n    if (notes) order.notes = notes;\n    localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(orders));\n    // إضافة إشعار\n    addNotification({\n        title: 'تحديث حالة الطلب',\n        message: `تم تحديث حالة الطلب ${order.trackingNumber} إلى ${getStatusLabel(status)}`,\n        type: 'info'\n    });\n    return true;\n}\n// حذف طلب\nfunction deleteOrder(orderId) {\n    const orders = getOrders();\n    const filteredOrders = orders.filter((o)=>o.id !== orderId);\n    if (filteredOrders.length === orders.length) return false;\n    localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(filteredOrders));\n    return true;\n}\n// البحث في الطلبات\nfunction searchOrders(query) {\n    const orders = getOrders();\n    const searchTerm = query.toLowerCase();\n    return orders.filter((order)=>order.trackingNumber.toLowerCase().includes(searchTerm) || order.senderName.toLowerCase().includes(searchTerm) || order.recipientName.toLowerCase().includes(searchTerm) || order.recipientPhone.includes(searchTerm) || order.senderPhone.includes(searchTerm));\n}\n// فلترة الطلبات حسب الحالة\nfunction filterOrdersByStatus(status) {\n    return getOrders().filter((order)=>order.status === status);\n}\n// فلترة الطلبات حسب المندوب\nfunction filterOrdersByCourier(courierId) {\n    return getOrders().filter((order)=>order.courierId === courierId);\n}\n// إسناد طلبات لمندوب\nfunction assignOrdersToCourier(orderIds, courierId, courierName, courierPhone) {\n    const orders = getOrders();\n    let updated = false;\n    orders.forEach((order)=>{\n        if (orderIds.includes(order.id)) {\n            order.courierId = courierId;\n            order.courierName = courierName;\n            order.courierPhone = courierPhone;\n            order.status = 'processing';\n            order.updatedAt = new Date().toISOString();\n            // إضافة تحديث للحالة\n            order.statusHistory.push({\n                id: Date.now().toString() + Math.random(),\n                orderId: order.id,\n                status: 'processing',\n                notes: `تم إسناد الطلب للمندوب ${courierName}`,\n                updatedBy: 'النظام',\n                updatedAt: new Date().toISOString()\n            });\n            updated = true;\n        }\n    });\n    if (updated) {\n        localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(orders));\n    }\n    return updated;\n}\n// تخصيص الطلبات للمندوبين\nfunction assignOrderToCourier(orderId, courierId, courierName) {\n    const orders = getOrders();\n    const orderIndex = orders.findIndex((order)=>order.id === orderId);\n    if (orderIndex === -1) return false;\n    orders[orderIndex] = {\n        ...orders[orderIndex],\n        courierId,\n        courierName,\n        updatedAt: new Date().toISOString()\n    };\n    localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(orders));\n    return true;\n}\n// دوال إدارة الموظفين\nfunction getEmployees() {\n    const users = getUsers();\n    return users.filter((user)=>user.role !== 'manager');\n}\nfunction addEmployee(employeeData) {\n    const users = getUsers();\n    const newEmployee = {\n        ...employeeData,\n        id: generateId(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    };\n    users.push(newEmployee);\n    localStorage.setItem('users', JSON.stringify(users));\n    return newEmployee;\n}\nfunction updateEmployee(employeeId, updates) {\n    const users = getUsers();\n    const index = users.findIndex((user)=>user.id === employeeId);\n    if (index === -1) return false;\n    users[index] = {\n        ...users[index],\n        ...updates,\n        updatedAt: new Date().toISOString()\n    };\n    localStorage.setItem('users', JSON.stringify(users));\n    return true;\n}\nfunction deleteEmployee(employeeId) {\n    const users = getUsers();\n    const filteredUsers = users.filter((user)=>user.id !== employeeId);\n    if (filteredUsers.length === users.length) return false;\n    localStorage.setItem('users', JSON.stringify(filteredUsers));\n    return true;\n}\nfunction searchEmployees(query) {\n    const employees = getEmployees();\n    const searchTerm = query.toLowerCase();\n    return employees.filter((employee)=>employee.name.toLowerCase().includes(searchTerm) || employee.username.toLowerCase().includes(searchTerm) || employee.phone && employee.phone.includes(searchTerm) || employee.email && employee.email.toLowerCase().includes(searchTerm));\n}\n// حساب الإحصائيات\nfunction calculateStatistics(courierId) {\n    const orders = courierId ? filterOrdersByCourier(courierId) : getOrders();\n    const stats = {\n        totalOrders: orders.length,\n        pendingOrders: orders.filter((o)=>o.status === 'pending').length,\n        processingOrders: orders.filter((o)=>o.status === 'processing').length,\n        shippedOrders: orders.filter((o)=>o.status === 'shipped').length,\n        outForDeliveryOrders: orders.filter((o)=>o.status === 'out_for_delivery').length,\n        deliveredOrders: orders.filter((o)=>o.status === 'delivered').length,\n        returnedOrders: orders.filter((o)=>o.status === 'returned').length,\n        cancelledOrders: orders.filter((o)=>o.status === 'cancelled').length,\n        postponedOrders: orders.filter((o)=>o.status === 'postponed').length,\n        totalRevenue: orders.filter((o)=>o.status === 'delivered').reduce((sum, o)=>sum + o.amount, 0),\n        todayOrders: 0,\n        thisWeekOrders: 0,\n        thisMonthOrders: 0\n    };\n    // حساب الطلبات حسب التاريخ\n    const today = new Date();\n    const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()));\n    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);\n    orders.forEach((order)=>{\n        const orderDate = new Date(order.createdAt);\n        const todayDate = new Date();\n        if (orderDate.toDateString() === todayDate.toDateString()) {\n            stats.todayOrders++;\n        }\n        if (orderDate >= startOfWeek) {\n            stats.thisWeekOrders++;\n        }\n        if (orderDate >= startOfMonth) {\n            stats.thisMonthOrders++;\n        }\n    });\n    return stats;\n}\n// إدارة الإشعارات\nfunction getNotifications() {\n    if (true) return [];\n    const notificationsData = localStorage.getItem(STORAGE_KEYS.NOTIFICATIONS);\n    if (!notificationsData) return [];\n    try {\n        return JSON.parse(notificationsData);\n    } catch  {\n        return [];\n    }\n}\nfunction addNotification(notification) {\n    const notifications = getNotifications();\n    const newNotification = {\n        ...notification,\n        id: Date.now().toString(),\n        isRead: false,\n        createdAt: new Date().toISOString()\n    };\n    notifications.unshift(newNotification) // إضافة في المقدمة\n    ;\n    // الاحتفاظ بآخر 100 إشعار فقط\n    if (notifications.length > 100) {\n        notifications.splice(100);\n    }\n    localStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify(notifications));\n}\nfunction markNotificationAsRead(notificationId) {\n    const notifications = getNotifications();\n    const notification = notifications.find((n)=>n.id === notificationId);\n    if (!notification) return false;\n    notification.isRead = true;\n    localStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify(notifications));\n    return true;\n}\n// دالة مساعدة للحصول على تسمية الحالة\nfunction getStatusLabel(status) {\n    const statusLabels = {\n        pending: 'في الانتظار',\n        processing: 'قيد المعالجة',\n        shipped: 'تم الشحن',\n        out_for_delivery: 'قيد التوصيل',\n        delivered: 'تم التسليم',\n        returned: 'راجع',\n        cancelled: 'ملغي',\n        postponed: 'مؤجل'\n    };\n    return statusLabels[status] || status;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/storage.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   generateTrackingNumber: () => (/* binding */ generateTrackingNumber),\n/* harmony export */   shareViaWhatsApp: () => (/* binding */ shareViaWhatsApp),\n/* harmony export */   validateIraqiPhone: () => (/* binding */ validateIraqiPhone),\n/* harmony export */   validatePhoneNumber: () => (/* binding */ validatePhoneNumber)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// دوال مساعدة للتطبيق\nfunction formatDate(date) {\n    const d = new Date(date);\n    return d.toLocaleDateString('ar-IQ', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    });\n}\nfunction formatCurrency(amount) {\n    return new Intl.NumberFormat('ar-IQ', {\n        style: 'currency',\n        currency: 'IQD',\n        minimumFractionDigits: 0\n    }).format(amount);\n}\nfunction generateTrackingNumber() {\n    const prefix = 'MRS';\n    const timestamp = Date.now().toString().slice(-6);\n    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');\n    return `${prefix}${timestamp}${random}`;\n}\nfunction validatePhoneNumber(phone) {\n    // التحقق من أرقام الهواتف العراقية\n    const iraqiPhoneRegex = /^(\\+964|964|0)?(7[0-9]{9}|1[0-9]{9})$/;\n    return iraqiPhoneRegex.test(phone.replace(/\\s/g, ''));\n}\nfunction validateIraqiPhone(phone) {\n    return validatePhoneNumber(phone);\n}\nfunction copyToClipboard(text) {\n    return navigator.clipboard.writeText(text);\n}\nfunction shareViaWhatsApp(text) {\n    const encodedText = encodeURIComponent(text);\n    const url = `https://wa.me/?text=${encodedText}`;\n    window.open(url, '_blank');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Forders%2Fpage&page=%2Forders%2Fpage&appPaths=%2Forders%2Fpage&pagePath=private-next-app-dir%2Forders%2Fpage.tsx&appDir=E%3A%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5Cmarsal%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%D9%85%D9%86%D8%AF%D9%88%D8%A8%5Cmarsal&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();