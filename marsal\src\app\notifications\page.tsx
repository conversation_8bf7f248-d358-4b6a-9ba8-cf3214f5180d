'use client'

import React, { useState, useEffect } from 'react'
import { useAuth } from '@/components/auth-provider'
import { Header } from '@/components/header'
import { Navigation } from '@/components/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Bell, 
  BellRing, 
  Check, 
  Trash2, 
  Filter,
  Package,
  User,
  AlertCircle,
  CheckCircle,
  Clock,
  MessageSquare
} from 'lucide-react'
import { getOrders, getUsers } from '@/lib/storage'
import { formatDate } from '@/lib/utils'

interface Notification {
  id: string
  type: 'order_status' | 'new_order' | 'assignment' | 'system' | 'reminder'
  title: string
  message: string
  timestamp: string
  read: boolean
  priority: 'low' | 'medium' | 'high'
  relatedOrderId?: string
  relatedUserId?: string
}

export default function NotificationsPage() {
  const { user } = useAuth()
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [filteredNotifications, setFilteredNotifications] = useState<Notification[]>([])
  const [filter, setFilter] = useState<string>('all')
  const [priorityFilter, setPriorityFilter] = useState<string>('all')

  useEffect(() => {
    generateNotifications()
  }, [user])

  useEffect(() => {
    filterNotifications()
  }, [notifications, filter, priorityFilter])

  const generateNotifications = () => {
    if (!user) return

    const orders = getOrders()
    const users = getUsers()
    const generatedNotifications: Notification[] = []

    // إشعارات الطلبات الجديدة (للمديرين والمتابعين)
    if (user.role === 'manager' || user.role === 'supervisor') {
      const pendingOrders = orders.filter(order => order.status === 'pending')
      pendingOrders.slice(0, 5).forEach(order => {
        generatedNotifications.push({
          id: `new_order_${order.id}`,
          type: 'new_order',
          title: 'طلب جديد',
          message: `طلب جديد من ${order.customerName} بقيمة ${order.totalAmount} د.ع`,
          timestamp: order.createdAt,
          read: Math.random() > 0.7,
          priority: 'medium',
          relatedOrderId: order.id
        })
      })

      // إشعارات الطلبات المتأخرة
      const overdueOrders = orders.filter(order => {
        const orderDate = new Date(order.createdAt)
        const daysDiff = (new Date().getTime() - orderDate.getTime()) / (1000 * 3600 * 24)
        return daysDiff > 2 && ['pending', 'confirmed', 'assigned'].includes(order.status)
      })

      overdueOrders.slice(0, 3).forEach(order => {
        generatedNotifications.push({
          id: `overdue_${order.id}`,
          type: 'reminder',
          title: 'طلب متأخر',
          message: `الطلب ${order.id} متأخر منذ أكثر من يومين`,
          timestamp: new Date().toISOString(),
          read: false,
          priority: 'high',
          relatedOrderId: order.id
        })
      })
    }

    // إشعارات المندوبين
    if (user.role === 'courier') {
      const myOrders = orders.filter(order => order.assignedTo === user.id)
      
      // الطلبات المُسندة حديثاً
      const recentAssignments = myOrders.filter(order => 
        order.status === 'assigned' && 
        new Date(order.updatedAt).getTime() > new Date().getTime() - (24 * 60 * 60 * 1000)
      )

      recentAssignments.forEach(order => {
        generatedNotifications.push({
          id: `assignment_${order.id}`,
          type: 'assignment',
          title: 'طلب جديد مُسند إليك',
          message: `تم إسناد طلب ${order.customerName} إليك`,
          timestamp: order.updatedAt,
          read: Math.random() > 0.5,
          priority: 'high',
          relatedOrderId: order.id
        })
      })

      // تذكير بالطلبات قيد التوصيل
      const outForDelivery = myOrders.filter(order => order.status === 'out_for_delivery')
      outForDelivery.slice(0, 3).forEach(order => {
        generatedNotifications.push({
          id: `delivery_reminder_${order.id}`,
          type: 'reminder',
          title: 'تذكير بالتوصيل',
          message: `لا تنس توصيل طلب ${order.customerName}`,
          timestamp: new Date().toISOString(),
          read: Math.random() > 0.8,
          priority: 'medium',
          relatedOrderId: order.id
        })
      })
    }

    // إشعارات النظام العامة
    generatedNotifications.push(
      {
        id: 'system_1',
        type: 'system',
        title: 'تحديث النظام',
        message: 'تم تحديث النظام إلى الإصدار الجديد بنجاح',
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        read: true,
        priority: 'low'
      },
      {
        id: 'system_2',
        type: 'system',
        title: 'نصائح الاستخدام',
        message: 'يمكنك استخدام البحث السريع للعثور على الطلبات بسهولة',
        timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        read: Math.random() > 0.6,
        priority: 'low'
      }
    )

    // ترتيب الإشعارات حسب التاريخ (الأحدث أولاً)
    generatedNotifications.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    
    setNotifications(generatedNotifications)
  }

  const filterNotifications = () => {
    let filtered = notifications

    if (filter !== 'all') {
      if (filter === 'unread') {
        filtered = filtered.filter(n => !n.read)
      } else {
        filtered = filtered.filter(n => n.type === filter)
      }
    }

    if (priorityFilter !== 'all') {
      filtered = filtered.filter(n => n.priority === priorityFilter)
    }

    setFilteredNotifications(filtered)
  }

  const markAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId ? { ...n, read: true } : n
      )
    )
  }

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(n => ({ ...n, read: true }))
    )
  }

  const deleteNotification = (notificationId: string) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId))
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'new_order':
        return <Package className="h-5 w-5 text-blue-600" />
      case 'assignment':
        return <User className="h-5 w-5 text-green-600" />
      case 'order_status':
        return <CheckCircle className="h-5 w-5 text-purple-600" />
      case 'reminder':
        return <Clock className="h-5 w-5 text-orange-600" />
      case 'system':
        return <MessageSquare className="h-5 w-5 text-gray-600" />
      default:
        return <Bell className="h-5 w-5 text-gray-600" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'low':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'عالية'
      case 'medium':
        return 'متوسطة'
      case 'low':
        return 'منخفضة'
      default:
        return 'عادية'
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'new_order':
        return 'طلب جديد'
      case 'assignment':
        return 'إسناد'
      case 'order_status':
        return 'حالة الطلب'
      case 'reminder':
        return 'تذكير'
      case 'system':
        return 'النظام'
      default:
        return 'عام'
    }
  }

  const unreadCount = notifications.filter(n => !n.read).length

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="flex">
        <Navigation />
        
        <main className="flex-1 p-6">
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">الإشعارات</h1>
                <p className="text-gray-600">إدارة الإشعارات والتنبيهات</p>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-lg px-3 py-1">
                  {unreadCount} غير مقروء
                </Badge>
                <Button onClick={markAllAsRead} variant="outline" size="sm">
                  <Check className="h-4 w-4 ml-1" />
                  تحديد الكل كمقروء
                </Button>
              </div>
            </div>
          </div>

          {/* إحصائيات سريعة */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Bell className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">إجمالي الإشعارات</p>
                    <p className="text-2xl font-bold text-gray-900">{notifications.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <BellRing className="h-6 w-6 text-orange-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">غير مقروء</p>
                    <p className="text-2xl font-bold text-gray-900">{unreadCount}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-red-100 rounded-lg">
                    <AlertCircle className="h-6 w-6 text-red-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">أولوية عالية</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {notifications.filter(n => n.priority === 'high').length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">مقروء</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {notifications.filter(n => n.read).length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* أدوات التحكم */}
          <div className="mb-6 flex flex-col sm:flex-row gap-4">
            <Select value={filter} onValueChange={setFilter}>
              <SelectTrigger className="w-48">
                <Filter className="h-4 w-4 ml-2" />
                <SelectValue placeholder="فلترة حسب النوع" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الإشعارات</SelectItem>
                <SelectItem value="unread">غير مقروء</SelectItem>
                <SelectItem value="new_order">طلبات جديدة</SelectItem>
                <SelectItem value="assignment">إسناد</SelectItem>
                <SelectItem value="reminder">تذكيرات</SelectItem>
                <SelectItem value="system">النظام</SelectItem>
              </SelectContent>
            </Select>

            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-48">
                <AlertCircle className="h-4 w-4 ml-2" />
                <SelectValue placeholder="فلترة حسب الأولوية" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الأولويات</SelectItem>
                <SelectItem value="high">عالية</SelectItem>
                <SelectItem value="medium">متوسطة</SelectItem>
                <SelectItem value="low">منخفضة</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* قائمة الإشعارات */}
          <div className="space-y-4">
            {filteredNotifications.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">لا توجد إشعارات</h3>
                  <p className="text-gray-600">لا توجد إشعارات تطابق معايير البحث</p>
                </CardContent>
              </Card>
            ) : (
              filteredNotifications.map((notification) => (
                <Card 
                  key={notification.id} 
                  className={`hover:shadow-md transition-shadow ${!notification.read ? 'border-l-4 border-l-blue-500 bg-blue-50/30' : ''}`}
                >
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="flex-shrink-0 mt-1">
                        {getNotificationIcon(notification.type)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h3 className={`text-lg font-semibold ${!notification.read ? 'text-gray-900' : 'text-gray-700'}`}>
                              {notification.title}
                            </h3>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="outline" className="text-xs">
                                {getTypeLabel(notification.type)}
                              </Badge>
                              <Badge className={`text-xs ${getPriorityColor(notification.priority)}`}>
                                {getPriorityLabel(notification.priority)}
                              </Badge>
                              {!notification.read && (
                                <Badge className="bg-blue-100 text-blue-800 text-xs">
                                  جديد
                                </Badge>
                              )}
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            {!notification.read && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => markAsRead(notification.id)}
                              >
                                <Check className="h-4 w-4" />
                              </Button>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => deleteNotification(notification.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        
                        <p className={`text-sm mb-3 ${!notification.read ? 'text-gray-900' : 'text-gray-600'}`}>
                          {notification.message}
                        </p>
                        
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>{formatDate(notification.timestamp)}</span>
                          {notification.relatedOrderId && (
                            <span>الطلب: {notification.relatedOrderId}</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </main>
      </div>
    </div>
  )
}
