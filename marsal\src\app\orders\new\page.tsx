'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/auth-provider'
import { Header } from '@/components/header'
import { Navigation } from '@/components/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { addOrder } from '@/lib/storage'
import { validateIraqiPhone } from '@/lib/utils'
import { Order, OrderStatus } from '@/types'
import { Save, ArrowRight } from 'lucide-react'

export default function NewOrderPage() {
  const { user, isLoading } = useAuth()
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const [formData, setFormData] = useState({
    senderName: '',
    senderPhone: '',
    senderAddress: '',
    recipientName: '',
    recipientPhone: '',
    recipientAddress: '',
    amount: '',
    description: '',
    notes: '',
    status: 'pending' as OrderStatus,
  })

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/login')
      return
    }

    // التحقق من الصلاحيات
    if (user && user.role === 'courier') {
      router.push('/orders')
      return
    }
  }, [user, isLoading, router])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // إزالة الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // التحقق من الحقول المطلوبة
    if (!formData.senderName.trim()) {
      newErrors.senderName = 'اسم المرسل مطلوب'
    }

    if (!formData.senderPhone.trim()) {
      newErrors.senderPhone = 'رقم هاتف المرسل مطلوب'
    } else if (!validateIraqiPhone(formData.senderPhone)) {
      newErrors.senderPhone = 'رقم الهاتف غير صحيح'
    }

    if (!formData.recipientName.trim()) {
      newErrors.recipientName = 'اسم المستلم مطلوب'
    }

    if (!formData.recipientPhone.trim()) {
      newErrors.recipientPhone = 'رقم هاتف المستلم مطلوب'
    } else if (!validateIraqiPhone(formData.recipientPhone)) {
      newErrors.recipientPhone = 'رقم الهاتف غير صحيح'
    }

    if (!formData.recipientAddress.trim()) {
      newErrors.recipientAddress = 'عنوان المستلم مطلوب'
    }

    if (!formData.amount.trim()) {
      newErrors.amount = 'المبلغ مطلوب'
    } else {
      const amount = parseFloat(formData.amount)
      if (isNaN(amount) || amount <= 0) {
        newErrors.amount = 'المبلغ يجب أن يكون رقماً موجباً'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      const orderData = {
        senderName: formData.senderName.trim(),
        senderPhone: formData.senderPhone.trim(),
        senderAddress: formData.senderAddress.trim(),
        recipientName: formData.recipientName.trim(),
        recipientPhone: formData.recipientPhone.trim(),
        recipientAddress: formData.recipientAddress.trim(),
        amount: parseFloat(formData.amount),
        description: formData.description.trim(),
        notes: formData.notes.trim(),
        status: formData.status,
      }

      const newOrder = addOrder(orderData)
      
      // إعادة التوجيه إلى صفحة تفاصيل الطلب
      router.push(`/orders/${newOrder.id}`)
    } catch (error) {
      console.error('Error creating order:', error)
      setErrors({ submit: 'حدث خطأ أثناء إنشاء الطلب' })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!user || user.role === 'courier') return null

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="flex">
        <Navigation />
        
        <main className="flex-1 p-6">
          {/* العنوان */}
          <div className="flex items-center space-x-4 space-x-reverse mb-6">
            <Button
              variant="outline"
              onClick={() => router.back()}
              className="flex items-center space-x-2 space-x-reverse"
            >
              <ArrowRight className="h-4 w-4" />
              <span>رجوع</span>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">طلب جديد</h1>
              <p className="text-gray-600 mt-1">إضافة طلب توصيل جديد</p>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="max-w-4xl">
            <div className="grid gap-6">
              {/* بيانات المرسل */}
              <Card>
                <CardHeader>
                  <CardTitle>بيانات المرسل</CardTitle>
                  <CardDescription>معلومات الشخص أو الشركة المرسلة</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="senderName">اسم المرسل *</Label>
                      <Input
                        id="senderName"
                        value={formData.senderName}
                        onChange={(e) => handleInputChange('senderName', e.target.value)}
                        placeholder="أدخل اسم المرسل"
                        className={errors.senderName ? 'border-red-500' : ''}
                      />
                      {errors.senderName && (
                        <p className="text-sm text-red-600 mt-1">{errors.senderName}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="senderPhone">رقم الهاتف *</Label>
                      <Input
                        id="senderPhone"
                        value={formData.senderPhone}
                        onChange={(e) => handleInputChange('senderPhone', e.target.value)}
                        placeholder="07xxxxxxxxx"
                        className={errors.senderPhone ? 'border-red-500' : ''}
                      />
                      {errors.senderPhone && (
                        <p className="text-sm text-red-600 mt-1">{errors.senderPhone}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="senderAddress">العنوان</Label>
                    <Input
                      id="senderAddress"
                      value={formData.senderAddress}
                      onChange={(e) => handleInputChange('senderAddress', e.target.value)}
                      placeholder="عنوان المرسل (اختياري)"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* بيانات المستلم */}
              <Card>
                <CardHeader>
                  <CardTitle>بيانات المستلم</CardTitle>
                  <CardDescription>معلومات الشخص المستلم للطلب</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="recipientName">اسم المستلم *</Label>
                      <Input
                        id="recipientName"
                        value={formData.recipientName}
                        onChange={(e) => handleInputChange('recipientName', e.target.value)}
                        placeholder="أدخل اسم المستلم"
                        className={errors.recipientName ? 'border-red-500' : ''}
                      />
                      {errors.recipientName && (
                        <p className="text-sm text-red-600 mt-1">{errors.recipientName}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="recipientPhone">رقم الهاتف *</Label>
                      <Input
                        id="recipientPhone"
                        value={formData.recipientPhone}
                        onChange={(e) => handleInputChange('recipientPhone', e.target.value)}
                        placeholder="07xxxxxxxxx"
                        className={errors.recipientPhone ? 'border-red-500' : ''}
                      />
                      {errors.recipientPhone && (
                        <p className="text-sm text-red-600 mt-1">{errors.recipientPhone}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="recipientAddress">العنوان *</Label>
                    <Input
                      id="recipientAddress"
                      value={formData.recipientAddress}
                      onChange={(e) => handleInputChange('recipientAddress', e.target.value)}
                      placeholder="العنوان الكامل للتوصيل"
                      className={errors.recipientAddress ? 'border-red-500' : ''}
                    />
                    {errors.recipientAddress && (
                      <p className="text-sm text-red-600 mt-1">{errors.recipientAddress}</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* تفاصيل الطلب */}
              <Card>
                <CardHeader>
                  <CardTitle>تفاصيل الطلب</CardTitle>
                  <CardDescription>معلومات الطلب والمبلغ</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="amount">المبلغ (دينار عراقي) *</Label>
                      <Input
                        id="amount"
                        type="number"
                        min="0"
                        step="1000"
                        value={formData.amount}
                        onChange={(e) => handleInputChange('amount', e.target.value)}
                        placeholder="0"
                        className={errors.amount ? 'border-red-500' : ''}
                      />
                      {errors.amount && (
                        <p className="text-sm text-red-600 mt-1">{errors.amount}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="status">حالة الطلب</Label>
                      <select
                        id="status"
                        value={formData.status}
                        onChange={(e) => handleInputChange('status', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="pending">في الانتظار</option>
                        <option value="processing">قيد المعالجة</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="description">وصف الطلب</Label>
                    <Input
                      id="description"
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="وصف مختصر للطلب (اختياري)"
                    />
                  </div>

                  <div>
                    <Label htmlFor="notes">ملاحظات</Label>
                    <textarea
                      id="notes"
                      value={formData.notes}
                      onChange={(e) => handleInputChange('notes', e.target.value)}
                      placeholder="ملاحظات إضافية (اختياري)"
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* أزرار الإجراءات */}
              <div className="flex justify-end space-x-4 space-x-reverse">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                  disabled={isSubmitting}
                >
                  إلغاء
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex items-center space-x-2 space-x-reverse"
                >
                  <Save className="h-4 w-4" />
                  <span>{isSubmitting ? 'جاري الحفظ...' : 'حفظ الطلب'}</span>
                </Button>
              </div>

              {errors.submit && (
                <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
                  {errors.submit}
                </div>
              )}
            </div>
          </form>
        </main>
      </div>
    </div>
  )
}
