{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/node_modules/%40swc/helpers/cjs/_interop_require_default.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexports._ = _interop_require_default;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,yBAAyB,GAAG;IACjC,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,SAAS;IAAI;AACxD;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/node_modules/next/src/client/components/http-access-fallback/error-fallback.tsx"], "sourcesContent": ["import React from 'react'\n\nconst styles: Record<string, React.CSSProperties> = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n\n  desc: {\n    display: 'inline-block',\n  },\n\n  h1: {\n    display: 'inline-block',\n    margin: '0 20px 0 0',\n    padding: '0 23px 0 0',\n    fontSize: 24,\n    fontWeight: 500,\n    verticalAlign: 'top',\n    lineHeight: '49px',\n  },\n\n  h2: {\n    fontSize: 14,\n    fontWeight: 400,\n    lineHeight: '49px',\n    margin: 0,\n  },\n}\n\nexport function HTTPAccessErrorFallback({\n  status,\n  message,\n}: {\n  status: number\n  message: string\n}) {\n  return (\n    <>\n      {/* <head> */}\n      <title>{`${status}: ${message}`}</title>\n      {/* </head> */}\n      <div style={styles.error}>\n        <div>\n          <style\n            dangerouslySetInnerHTML={{\n              /* Minified CSS from\n                body { margin: 0; color: #000; background: #fff; }\n                .next-error-h1 {\n                  border-right: 1px solid rgba(0, 0, 0, .3);\n                }\n\n                @media (prefers-color-scheme: dark) {\n                  body { color: #fff; background: #000; }\n                  .next-error-h1 {\n                    border-right: 1px solid rgba(255, 255, 255, .3);\n                  }\n                }\n              */\n              __html: `body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}`,\n            }}\n          />\n          <h1 className=\"next-error-h1\" style={styles.h1}>\n            {status}\n          </h1>\n          <div style={styles.desc}>\n            <h2 style={styles.h2}>{message}</h2>\n          </div>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": ["HTTPAccessErrorFallback", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "desc", "h1", "margin", "padding", "fontSize", "fontWeight", "verticalAlign", "lineHeight", "h2", "status", "message", "title", "div", "style", "dangerouslySetInnerHTML", "__html", "className"], "mappings": ";;;;+BAqCgBA,2BAAAA;;;eAAAA;;;;;gEArCE;AAElB,MAAMC,SAA8C;IAClDC,OAAO;QACL,0FAA0F;QAC1FC,YACE;QACFC,QAAQ;QACRC,WAAW;QACXC,SAAS;QACTC,eAAe;QACfC,YAAY;QACZC,gBAAgB;IAClB;IAEAC,MAAM;QACJJ,SAAS;IACX;IAEAK,IAAI;QACFL,SAAS;QACTM,QAAQ;QACRC,SAAS;QACTC,UAAU;QACVC,YAAY;QACZC,eAAe;QACfC,YAAY;IACd;IAEAC,IAAI;QACFJ,UAAU;QACVC,YAAY;QACZE,YAAY;QACZL,QAAQ;IACV;AACF;AAEO,SAASZ,wBAAwB,KAMvC;IANuC,IAAA,EACtCmB,MAAM,EACNC,OAAO,EAIR,GANuC;IAOtC,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,IAAA,EAAA,YAAA,QAAA,EAAA;;0BAEE,CAAA,GAAA,YAAA,GAAA,EAACC,SAAAA;0BAAUF,SAAO,OAAIC;;0BAEtB,CAAA,GAAA,YAAA,GAAA,EAACE,OAAAA;gBAAIC,OAAOtB,OAAOC,KAAK;0BACtB,WAAA,GAAA,CAAA,GAAA,YAAA,IAAA,EAACoB,OAAAA;;sCACC,CAAA,GAAA,YAAA,GAAA,EAACC,SAAAA;4BACCC,yBAAyB;gCACvB;;;;;;;;;;;;cAYA,GACAC,QAAS;4BACX;;sCAEF,CAAA,GAAA,YAAA,GAAA,EAACd,MAAAA;4BAAGe,WAAU;4BAAgBH,OAAOtB,OAAOU,EAAE;sCAC3CQ;;sCAEH,CAAA,GAAA,YAAA,GAAA,EAACG,OAAAA;4BAAIC,OAAOtB,OAAOS,IAAI;sCACrB,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACQ,MAAAA;gCAAGK,OAAOtB,OAAOiB,EAAE;0CAAGE;;;;;;;;AAMnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/node_modules/next/src/client/components/not-found-error.tsx"], "sourcesContent": ["import { HTTPAccessErrorFallback } from './http-access-fallback/error-fallback'\n\nexport default function NotFound() {\n  return (\n    <HTTPAccessErrorFallback\n      status={404}\n      message=\"This page could not be found.\"\n    />\n  )\n}\n"], "names": ["NotFound", "HTTPAccessErrorFallback", "status", "message"], "mappings": ";;;;+BAEA,WAAA;;;eAAwBA;;;;+BAFgB;AAEzB,SAASA;IACtB,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACC,eAAAA,uBAAuB,EAAA;QACtBC,QAAQ;QACRC,SAAQ;;AAGd", "ignoreList": [0], "debugId": null}}]}