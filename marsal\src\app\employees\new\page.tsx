'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/auth-provider'
import { Header } from '@/components/header'
import { Navigation } from '@/components/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { addEmployee, getUsers } from '@/lib/storage'
import { validateIraqiPhone } from '@/lib/utils'
import { UserRole } from '@/types'
import { Save, ArrowRight } from 'lucide-react'

export default function NewEmployeePage() {
  const { user, isLoading } = useAuth()
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const [formData, setFormData] = useState({
    name: '',
    username: '',
    password: '',
    confirmPassword: '',
    role: 'courier' as UserRole,
    phone: '',
    email: '',
    address: '',
  })

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/login')
      return
    }

    // التحقق من الصلاحيات - فقط المدير والمتابع يمكنهم إضافة موظفين
    if (user && user.role === 'courier') {
      router.push('/employees')
      return
    }
  }, [user, isLoading, router])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // إزالة الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // التحقق من الحقول المطلوبة
    if (!formData.name.trim()) {
      newErrors.name = 'الاسم مطلوب'
    }

    if (!formData.username.trim()) {
      newErrors.username = 'اسم المستخدم مطلوب'
    } else {
      // التحقق من عدم تكرار اسم المستخدم
      const existingUsers = getUsers()
      if (existingUsers.some(u => u.username === formData.username.trim())) {
        newErrors.username = 'اسم المستخدم موجود بالفعل'
      }
    }

    if (!formData.password.trim()) {
      newErrors.password = 'كلمة المرور مطلوبة'
    } else if (formData.password.length < 6) {
      newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'كلمة المرور غير متطابقة'
    }

    // التحقق من رقم الهاتف إذا تم إدخاله
    if (formData.phone.trim() && !validateIraqiPhone(formData.phone)) {
      newErrors.phone = 'رقم الهاتف غير صحيح'
    }

    // التحقق من البريد الإلكتروني إذا تم إدخاله
    if (formData.email.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(formData.email)) {
        newErrors.email = 'البريد الإلكتروني غير صحيح'
      }
    }

    // التحقق من صلاحيات إنشاء الأدوار
    if (user?.role === 'supervisor' && formData.role !== 'courier') {
      newErrors.role = 'المتابع يمكنه إضافة مندوبين فقط'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      const employeeData = {
        name: formData.name.trim(),
        username: formData.username.trim(),
        password: formData.password,
        role: formData.role,
        phone: formData.phone.trim() || undefined,
        email: formData.email.trim() || undefined,
        address: formData.address.trim() || undefined,
      }

      const newEmployee = addEmployee(employeeData)
      
      // إعادة التوجيه إلى صفحة الموظفين
      router.push('/employees')
    } catch (error) {
      console.error('Error creating employee:', error)
      setErrors({ submit: 'حدث خطأ أثناء إنشاء الموظف' })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!user || user.role === 'courier') return null

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="flex">
        <Navigation />
        
        <main className="flex-1 p-6">
          {/* العنوان */}
          <div className="flex items-center space-x-4 space-x-reverse mb-6">
            <Button
              variant="outline"
              onClick={() => router.back()}
              className="flex items-center space-x-2 space-x-reverse"
            >
              <ArrowRight className="h-4 w-4" />
              <span>رجوع</span>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">موظف جديد</h1>
              <p className="text-gray-600 mt-1">إضافة موظف جديد إلى النظام</p>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="max-w-4xl">
            <div className="grid gap-6">
              {/* المعلومات الأساسية */}
              <Card>
                <CardHeader>
                  <CardTitle>المعلومات الأساسية</CardTitle>
                  <CardDescription>البيانات الشخصية للموظف</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">الاسم الكامل *</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="أدخل الاسم الكامل"
                        className={errors.name ? 'border-red-500' : ''}
                      />
                      {errors.name && (
                        <p className="text-sm text-red-600 mt-1">{errors.name}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="role">الدور *</Label>
                      <select
                        id="role"
                        value={formData.role}
                        onChange={(e) => handleInputChange('role', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          errors.role ? 'border-red-500' : 'border-gray-300'
                        }`}
                      >
                        {user.role === 'manager' && (
                          <option value="supervisor">متابع</option>
                        )}
                        <option value="courier">مندوب</option>
                      </select>
                      {errors.role && (
                        <p className="text-sm text-red-600 mt-1">{errors.role}</p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="phone">رقم الهاتف</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        placeholder="07xxxxxxxxx"
                        className={errors.phone ? 'border-red-500' : ''}
                      />
                      {errors.phone && (
                        <p className="text-sm text-red-600 mt-1">{errors.phone}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="email">البريد الإلكتروني</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        placeholder="<EMAIL>"
                        className={errors.email ? 'border-red-500' : ''}
                      />
                      {errors.email && (
                        <p className="text-sm text-red-600 mt-1">{errors.email}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="address">العنوان</Label>
                    <Input
                      id="address"
                      value={formData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      placeholder="عنوان الموظف (اختياري)"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* بيانات تسجيل الدخول */}
              <Card>
                <CardHeader>
                  <CardTitle>بيانات تسجيل الدخول</CardTitle>
                  <CardDescription>معلومات الدخول إلى النظام</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="username">اسم المستخدم *</Label>
                    <Input
                      id="username"
                      value={formData.username}
                      onChange={(e) => handleInputChange('username', e.target.value)}
                      placeholder="اسم المستخدم للدخول"
                      className={errors.username ? 'border-red-500' : ''}
                    />
                    {errors.username && (
                      <p className="text-sm text-red-600 mt-1">{errors.username}</p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="password">كلمة المرور *</Label>
                      <Input
                        id="password"
                        type="password"
                        value={formData.password}
                        onChange={(e) => handleInputChange('password', e.target.value)}
                        placeholder="كلمة المرور"
                        className={errors.password ? 'border-red-500' : ''}
                      />
                      {errors.password && (
                        <p className="text-sm text-red-600 mt-1">{errors.password}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="confirmPassword">تأكيد كلمة المرور *</Label>
                      <Input
                        id="confirmPassword"
                        type="password"
                        value={formData.confirmPassword}
                        onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                        placeholder="تأكيد كلمة المرور"
                        className={errors.confirmPassword ? 'border-red-500' : ''}
                      />
                      {errors.confirmPassword && (
                        <p className="text-sm text-red-600 mt-1">{errors.confirmPassword}</p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* أزرار الإجراءات */}
              <div className="flex justify-end space-x-4 space-x-reverse">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                  disabled={isSubmitting}
                >
                  إلغاء
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex items-center space-x-2 space-x-reverse"
                >
                  <Save className="h-4 w-4" />
                  <span>{isSubmitting ? 'جاري الحفظ...' : 'حفظ الموظف'}</span>
                </Button>
              </div>

              {errors.submit && (
                <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
                  {errors.submit}
                </div>
              )}
            </div>
          </form>
        </main>
      </div>
    </div>
  )
}
