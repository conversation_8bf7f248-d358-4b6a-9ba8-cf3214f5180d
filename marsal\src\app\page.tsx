'use client'

import React, { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/auth-provider'
import { Header } from '@/components/header'
import { Navigation } from '@/components/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { SectionCard } from '@/components/cards/section-card'
import { BranchCard } from '@/components/cards/branch-card'
import { OrderCard } from '@/components/cards/order-card'
import { calculateStatistics } from '@/lib/storage'
import {
  Package,
  Users,
  BarChart3,
  Archive,
  Settings,
  Bell,
  Truck,
  RotateCcw,
  Calculator,
  Download,
  Building2,
  Star,
  TrendingUp,
  Clock,
  CheckCircle,
  DollarSign
} from 'lucide-react'

interface DashboardCard {
  title: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  href: string
  gradient: string
  roles: string[]
}

// بيانات تجريبية للفروع
const branches = [
  {
    id: 'BR001',
    name: 'فرع بغداد المركزي',
    address: 'شارع الرشيد، منطقة الكرخ، بغداد',
    phone: '07701234567',
    manager: 'أحمد محمد علي',
    totalOrders: 450,
    activeOrders: 25,
    completedOrders: 400,
    employees: 12,
    status: 'active' as const,
    workingHours: '8:00 ص - 8:00 م',
    performance: 95
  },
  {
    id: 'BR002',
    name: 'فرع البصرة',
    address: 'شارع الكورنيش، مركز البصرة',
    phone: '07707654321',
    manager: 'فاطمة حسن محمد',
    totalOrders: 320,
    activeOrders: 15,
    completedOrders: 290,
    employees: 8,
    status: 'active' as const,
    workingHours: '8:00 ص - 8:00 م',
    performance: 88
  },
  {
    id: 'BR003',
    name: 'فرع أربيل',
    address: 'شارع 60 متر، أربيل',
    phone: '07509876543',
    manager: 'كريم أحمد صالح',
    totalOrders: 280,
    activeOrders: 8,
    completedOrders: 260,
    employees: 6,
    status: 'maintenance' as const,
    workingHours: '9:00 ص - 7:00 م',
    performance: 75
  }
]

// بيانات تجريبية للطلبات الحديثة
const recentOrders = [
  {
    id: 'ORD001',
    trackingNumber: 'TRK123456',
    customerName: 'علي محمد حسن',
    customerPhone: '07701234567',
    customerAddress: 'حي الجامعة، شارع الأطباء، بناية 15، الطابق 3',
    amount: 75000,
    status: 'pending' as const,
    assignedTo: undefined,
    createdAt: '2024-01-15T10:30:00Z',
    deliveryDate: '2024-01-16T14:00:00Z',
    notes: 'يرجى الاتصال قبل التوصيل',
    priority: 'high' as const
  },
  {
    id: 'ORD002',
    trackingNumber: 'TRK123457',
    customerName: 'سارة أحمد علي',
    customerPhone: '07707654321',
    customerAddress: 'حي المنصور، شارع الأميرات، بناية 8',
    amount: 120000,
    status: 'in_transit' as const,
    assignedTo: 'محمد عبدالله',
    createdAt: '2024-01-14T09:15:00Z',
    deliveryDate: '2024-01-15T16:00:00Z',
    priority: 'medium' as const
  },
  {
    id: 'ORD003',
    trackingNumber: 'TRK123458',
    customerName: 'حسام طارق محمد',
    customerPhone: '07509876543',
    customerAddress: 'حي الكرادة، شارع أبو نواس، مجمع الزهور',
    amount: 95000,
    status: 'delivered' as const,
    assignedTo: 'أحمد صالح',
    createdAt: '2024-01-13T11:45:00Z',
    deliveryDate: '2024-01-14T13:30:00Z',
    priority: 'low' as const
  }
]

const dashboardCards: DashboardCard[] = [
  {
    title: 'إدارة الطلبات',
    description: 'إضافة وتعديل ومتابعة الطلبات',
    icon: Package,
    href: '/orders',
    gradient: 'from-blue-500 to-blue-600',
    roles: ['manager', 'supervisor', 'courier'],
  },
  {
    title: 'إسناد الطلبات',
    description: 'إسناد الطلبات للمندوبين',
    icon: Truck,
    href: '/dispatch',
    gradient: 'from-green-500 to-green-600',
    roles: ['manager'],
  },
  {
    title: 'إدارة الرواجع',
    description: 'متابعة الطلبات المرجعة',
    icon: RotateCcw,
    href: '/returns',
    gradient: 'from-orange-500 to-orange-600',
    roles: ['manager'],
  },
  {
    title: 'المحاسبة',
    description: 'التقارير المالية والحسابات',
    icon: Calculator,
    href: '/accounting',
    gradient: 'from-purple-500 to-purple-600',
    roles: ['manager'],
  },
  {
    title: 'الإحصائيات',
    description: 'تقارير وإحصائيات مفصلة',
    icon: BarChart3,
    href: '/statistics',
    gradient: 'from-indigo-500 to-indigo-600',
    roles: ['manager', 'supervisor', 'courier'],
  },
  {
    title: 'الأرشيف',
    description: 'أرشيف الطلبات المكتملة',
    icon: Archive,
    href: '/archive',
    gradient: 'from-gray-500 to-gray-600',
    roles: ['manager', 'supervisor', 'courier'],
  },
  {
    title: 'إدارة الموظفين',
    description: 'إضافة وإدارة المستخدمين',
    icon: Users,
    href: '/users',
    gradient: 'from-teal-500 to-teal-600',
    roles: ['manager', 'supervisor'],
  },
  {
    title: 'استيراد وتصدير',
    description: 'استيراد وتصدير البيانات',
    icon: Download,
    href: '/import-export',
    gradient: 'from-pink-500 to-pink-600',
    roles: ['manager'],
  },
  {
    title: 'الإشعارات',
    description: 'إدارة الإشعارات والتنبيهات',
    icon: Bell,
    href: '/notifications',
    gradient: 'from-yellow-500 to-yellow-600',
    roles: ['manager', 'supervisor', 'courier'],
  },
  {
    title: 'الإعدادات',
    description: 'إعدادات النظام والتطبيق',
    icon: Settings,
    href: '/settings',
    gradient: 'from-slate-500 to-slate-600',
    roles: ['manager', 'supervisor', 'courier'],
  },
]

export default function HomePage() {
  const { user, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/login')
    }
  }, [user, isLoading, router])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  // فلترة البطاقات حسب دور المستخدم
  const filteredCards = dashboardCards.filter(card =>
    card.roles.includes(user.role)
  )

  // حساب الإحصائيات
  const stats = calculateStatistics(user.role === 'courier' ? user.id : undefined)

  // تحويل البطاقات إلى تنسيق SectionCard
  const getSectionCards = () => {
    return filteredCards.map(card => ({
      title: card.title,
      description: card.description,
      icon: card.icon,
      href: card.href,
      color: card.gradient.includes('blue') ? 'blue' as const :
            card.gradient.includes('green') ? 'green' as const :
            card.gradient.includes('purple') ? 'purple' as const :
            card.gradient.includes('orange') ? 'orange' as const :
            card.gradient.includes('indigo') ? 'indigo' as const :
            card.gradient.includes('teal') ? 'teal' as const :
            card.gradient.includes('pink') ? 'pink' as const :
            card.gradient.includes('yellow') ? 'yellow' as const :
            'gray' as const,
      count: card.title === 'إدارة الطلبات' ? stats.totalOrders :
             card.title === 'إسناد الطلبات' ? stats.pendingOrders :
             card.title === 'إدارة الموظفين' ? stats.totalEmployees :
             card.title === 'الأرشيف' ? stats.deliveredOrders :
             undefined,
      trend: card.title === 'إدارة الطلبات' ? { value: 12, isPositive: true } :
             card.title === 'إسناد الطلبات' ? { value: 5, isPositive: false } :
             undefined
    }))
  }

  const sectionCards = getSectionCards()

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <Header />

      <div className="flex">
        <Navigation />

        <main className="flex-1 p-6">
          {/* ترحيب */}
          <div className="mb-8 animate-fade-in">
            <div className="flex items-center space-x-4 space-x-reverse mb-4">
              <div className="w-16 h-16 gradient-primary rounded-2xl flex items-center justify-center shadow-lg animate-bounce-in">
                <Star className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-4xl font-bold text-gradient mb-2">
                  مرحباً، {user.name}
                </h1>
                <p className="text-gray-600 text-lg">
                  {user.role === 'manager' && 'لوحة تحكم المدير - نظام إدارة التوصيل المتطور'}
                  {user.role === 'supervisor' && 'لوحة تحكم المتابع - نظام إدارة التوصيل المتطور'}
                  {user.role === 'courier' && 'لوحة تحكم المندوب - نظام إدارة التوصيل المتطور'}
                </p>
              </div>
            </div>
          </div>

          {/* إحصائيات سريعة */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card className="glass-effect border-0 hover-lift animate-slide-up">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">إجمالي الطلبات</p>
                    <p className="text-3xl font-bold text-blue-600">{stats.totalOrders}</p>
                    <div className="flex items-center mt-2">
                      <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                      <span className="text-sm text-green-600">+12%</span>
                    </div>
                  </div>
                  <div className="w-12 h-12 gradient-primary rounded-xl flex items-center justify-center shadow-lg">
                    <Package className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="glass-effect border-0 hover-lift animate-slide-up" style={{animationDelay: '0.1s'}}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">تم التسليم</p>
                    <p className="text-3xl font-bold text-green-600">{stats.deliveredOrders}</p>
                    <div className="flex items-center mt-2">
                      <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                      <span className="text-sm text-green-600">+8%</span>
                    </div>
                  </div>
                  <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                    <CheckCircle className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="glass-effect border-0 hover-lift animate-slide-up" style={{animationDelay: '0.2s'}}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">قيد التوصيل</p>
                    <p className="text-3xl font-bold text-orange-600">{stats.outForDeliveryOrders}</p>
                    <div className="flex items-center mt-2">
                      <Clock className="h-4 w-4 text-orange-500 mr-1" />
                      <span className="text-sm text-orange-600">نشط</span>
                    </div>
                  </div>
                  <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
                    <Truck className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="glass-effect border-0 hover-lift animate-slide-up" style={{animationDelay: '0.3s'}}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">إجمالي الإيرادات</p>
                    <p className="text-2xl font-bold text-purple-600">{stats.totalRevenue.toLocaleString()} د.ع</p>
                    <div className="flex items-center mt-2">
                      <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                      <span className="text-sm text-green-600">+15%</span>
                    </div>
                  </div>
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                    <DollarSign className="h-6 w-6 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* بطاقات الأقسام */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Building2 className="h-6 w-6 mr-2 text-blue-600" />
              أقسام النظام
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {sectionCards.map((card, index) => (
                <SectionCard
                  key={card.title}
                  title={card.title}
                  description={card.description}
                  icon={card.icon}
                  href={card.href}
                  color={card.color}
                  count={card.count}
                  trend={card.trend}
                />
              ))}
            </div>
          </div>

          {/* بطاقات الفروع */}
          {user.role === 'manager' && (
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <Building2 className="h-6 w-6 mr-2 text-green-600" />
                الفروع
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {branches.map((branch) => (
                  <BranchCard
                    key={branch.id}
                    id={branch.id}
                    name={branch.name}
                    address={branch.address}
                    phone={branch.phone}
                    manager={branch.manager}
                    totalOrders={branch.totalOrders}
                    activeOrders={branch.activeOrders}
                    completedOrders={branch.completedOrders}
                    employees={branch.employees}
                    status={branch.status}
                    workingHours={branch.workingHours}
                    performance={branch.performance}
                  />
                ))}
              </div>
            </div>
          )}

          {/* بطاقات الطلبات الحديثة */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Package className="h-6 w-6 mr-2 text-orange-600" />
              الطلبات الحديثة
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recentOrders.map((order) => (
                <OrderCard
                  key={order.id}
                  id={order.id}
                  trackingNumber={order.trackingNumber}
                  customerName={order.customerName}
                  customerPhone={order.customerPhone}
                  customerAddress={order.customerAddress}
                  amount={order.amount}
                  status={order.status}
                  assignedTo={order.assignedTo}
                  createdAt={order.createdAt}
                  deliveryDate={order.deliveryDate}
                  notes={order.notes}
                  priority={order.priority}
                />
              ))}
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}


