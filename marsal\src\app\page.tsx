'use client'

import React, { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/auth-provider'
import { Header } from '@/components/header'
import { Navigation } from '@/components/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { calculateStatistics } from '@/lib/storage'
import {
  Package,
  Users,
  BarChart3,
  Archive,
  Settings,
  Bell,
  Truck,
  RotateCcw,
  Calculator,
  Download,
} from 'lucide-react'

interface DashboardCard {
  title: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  href: string
  gradient: string
  roles: string[]
}

const dashboardCards: DashboardCard[] = [
  {
    title: 'إدارة الطلبات',
    description: 'إضافة وتعديل ومتابعة الطلبات',
    icon: Package,
    href: '/orders',
    gradient: 'from-blue-500 to-blue-600',
    roles: ['manager', 'supervisor', 'courier'],
  },
  {
    title: 'إسناد الطلبات',
    description: 'إسناد الطلبات للمندوبين',
    icon: Truck,
    href: '/dispatch',
    gradient: 'from-green-500 to-green-600',
    roles: ['manager'],
  },
  {
    title: 'إدارة الرواجع',
    description: 'متابعة الطلبات المرجعة',
    icon: RotateCcw,
    href: '/returns',
    gradient: 'from-orange-500 to-orange-600',
    roles: ['manager'],
  },
  {
    title: 'المحاسبة',
    description: 'التقارير المالية والحسابات',
    icon: Calculator,
    href: '/accounting',
    gradient: 'from-purple-500 to-purple-600',
    roles: ['manager'],
  },
  {
    title: 'الإحصائيات',
    description: 'تقارير وإحصائيات مفصلة',
    icon: BarChart3,
    href: '/statistics',
    gradient: 'from-indigo-500 to-indigo-600',
    roles: ['manager', 'supervisor', 'courier'],
  },
  {
    title: 'الأرشيف',
    description: 'أرشيف الطلبات المكتملة',
    icon: Archive,
    href: '/archive',
    gradient: 'from-gray-500 to-gray-600',
    roles: ['manager', 'supervisor', 'courier'],
  },
  {
    title: 'إدارة الموظفين',
    description: 'إضافة وإدارة المستخدمين',
    icon: Users,
    href: '/users',
    gradient: 'from-teal-500 to-teal-600',
    roles: ['manager', 'supervisor'],
  },
  {
    title: 'استيراد وتصدير',
    description: 'استيراد وتصدير البيانات',
    icon: Download,
    href: '/import-export',
    gradient: 'from-pink-500 to-pink-600',
    roles: ['manager'],
  },
  {
    title: 'الإشعارات',
    description: 'إدارة الإشعارات والتنبيهات',
    icon: Bell,
    href: '/notifications',
    gradient: 'from-yellow-500 to-yellow-600',
    roles: ['manager', 'supervisor', 'courier'],
  },
  {
    title: 'الإعدادات',
    description: 'إعدادات النظام والتطبيق',
    icon: Settings,
    href: '/settings',
    gradient: 'from-slate-500 to-slate-600',
    roles: ['manager', 'supervisor', 'courier'],
  },
]

export default function HomePage() {
  const { user, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/login')
    }
  }, [user, isLoading, router])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  // فلترة البطاقات حسب دور المستخدم
  const filteredCards = dashboardCards.filter(card =>
    card.roles.includes(user.role)
  )

  // حساب الإحصائيات
  const stats = calculateStatistics(user.role === 'courier' ? user.id : undefined)

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="flex">
        <Navigation />

        <main className="flex-1 p-6">
          {/* ترحيب */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              مرحباً، {user.name}
            </h1>
            <p className="text-gray-600">
              {user.role === 'manager' && 'لوحة تحكم المدير'}
              {user.role === 'supervisor' && 'لوحة تحكم المتابع'}
              {user.role === 'courier' && 'لوحة تحكم المندوب'}
            </p>
          </div>

          {/* إحصائيات سريعة */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Package className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalOrders}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Package className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">تم التسليم</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.deliveredOrders}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <Package className="h-6 w-6 text-orange-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">قيد التوصيل</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.outForDeliveryOrders}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Calculator className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">إجمالي الإيرادات</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalRevenue.toLocaleString()} د.ع</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* بطاقات الأقسام */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCards.map((card) => {
              const Icon = card.icon
              return (
                <Card
                  key={card.title}
                  className="cursor-pointer hover:shadow-lg transition-shadow duration-200"
                  onClick={() => router.push(card.href)}
                >
                  <CardHeader>
                    <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${card.gradient} flex items-center justify-center mb-4`}>
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                    <CardTitle className="text-xl">{card.title}</CardTitle>
                    <CardDescription>{card.description}</CardDescription>
                  </CardHeader>
                </Card>
              )
            })}
          </div>
        </main>
      </div>
    </div>
  )
}


