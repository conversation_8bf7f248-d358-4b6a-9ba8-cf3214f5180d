'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/auth-provider'
import { Header } from '@/components/header'
import { Navigation } from '@/components/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { getOrders, getEmployees, calculateStatistics } from '@/lib/storage'
import { Order, User, Statistics } from '@/types'
import { BarChart3, TrendingUp, Users, Package, Clock, Target, Calendar, Download } from 'lucide-react'
import { formatCurrency, formatDate } from '@/lib/utils'

interface DetailedStats extends Statistics {
  averageOrderValue: number
  ordersPerDay: number
  topCourier: { name: string; orders: number } | null
  recentTrends: {
    thisWeek: number
    lastWeek: number
    thisMonth: number
    lastMonth: number
  }
  statusDistribution: Array<{
    status: string
    count: number
    percentage: number
    label: string
  }>
}

export default function StatisticsPage() {
  const { user, isLoading } = useAuth()
  const router = useRouter()
  const [stats, setStats] = useState<DetailedStats | null>(null)
  const [dateRange, setDateRange] = useState({
    startDate: '',
    endDate: ''
  })
  const [isLoadingStats, setIsLoadingStats] = useState(true)

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/login')
      return
    }

    if (user) {
      loadStatistics()
    }
  }, [user, isLoading, router])

  const loadStatistics = () => {
    setIsLoadingStats(true)
    try {
      const orders = getOrders()
      const employees = getEmployees()
      
      // تطبيق فلتر التاريخ إذا كان محدداً
      let filteredOrders = orders
      if (dateRange.startDate && dateRange.endDate) {
        const startDate = new Date(dateRange.startDate)
        const endDate = new Date(dateRange.endDate)
        endDate.setHours(23, 59, 59, 999)
        
        filteredOrders = orders.filter(order => {
          const orderDate = new Date(order.createdAt)
          return orderDate >= startDate && orderDate <= endDate
        })
      }

      // حساب الإحصائيات الأساسية
      const basicStats = calculateStatistics(user?.role === 'courier' ? user.id : undefined)
      
      // حساب الإحصائيات المفصلة
      const totalAmount = filteredOrders.reduce((sum, order) => sum + order.amount, 0)
      const averageOrderValue = filteredOrders.length > 0 ? totalAmount / filteredOrders.length : 0
      
      // حساب الطلبات في اليوم
      const daysDiff = dateRange.startDate && dateRange.endDate 
        ? Math.ceil((new Date(dateRange.endDate).getTime() - new Date(dateRange.startDate).getTime()) / (1000 * 60 * 60 * 24))
        : 30 // افتراضي 30 يوم
      const ordersPerDay = filteredOrders.length / Math.max(daysDiff, 1)

      // أفضل مندوب
      const courierStats = employees
        .filter(emp => emp.role === 'courier')
        .map(courier => ({
          name: courier.name,
          orders: filteredOrders.filter(order => order.courierId === courier.id).length
        }))
        .sort((a, b) => b.orders - a.orders)
      
      const topCourier = courierStats.length > 0 ? courierStats[0] : null

      // الاتجاهات الحديثة
      const now = new Date()
      const thisWeekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay())
      const lastWeekStart = new Date(thisWeekStart.getTime() - 7 * 24 * 60 * 60 * 1000)
      const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1)
      const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1)

      const thisWeek = orders.filter(order => new Date(order.createdAt) >= thisWeekStart).length
      const lastWeek = orders.filter(order => {
        const orderDate = new Date(order.createdAt)
        return orderDate >= lastWeekStart && orderDate < thisWeekStart
      }).length
      const thisMonth = orders.filter(order => new Date(order.createdAt) >= thisMonthStart).length
      const lastMonth = orders.filter(order => {
        const orderDate = new Date(order.createdAt)
        return orderDate >= lastMonthStart && orderDate < thisMonthStart
      }).length

      // توزيع الحالات
      const statusCounts = filteredOrders.reduce((acc, order) => {
        acc[order.status] = (acc[order.status] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      const statusLabels = {
        pending: 'في الانتظار',
        processing: 'قيد المعالجة',
        shipped: 'تم الشحن',
        out_for_delivery: 'في الطريق للتسليم',
        delivered: 'تم التسليم',
        returned: 'مرجع',
        cancelled: 'ملغي',
        failed_delivery: 'فشل التسليم'
      }

      const statusDistribution = Object.entries(statusCounts).map(([status, count]) => ({
        status,
        count,
        percentage: (count / filteredOrders.length) * 100,
        label: statusLabels[status as keyof typeof statusLabels] || status
      })).sort((a, b) => b.count - a.count)

      const detailedStats: DetailedStats = {
        ...basicStats,
        averageOrderValue,
        ordersPerDay,
        topCourier,
        recentTrends: {
          thisWeek,
          lastWeek,
          thisMonth,
          lastMonth
        },
        statusDistribution
      }

      setStats(detailedStats)
    } catch (error) {
      console.error('Error loading statistics:', error)
    } finally {
      setIsLoadingStats(false)
    }
  }

  const handleDateRangeChange = (field: string, value: string) => {
    const newRange = { ...dateRange, [field]: value }
    setDateRange(newRange)
    
    if (newRange.startDate && newRange.endDate) {
      loadStatistics()
    }
  }

  const clearDateRange = () => {
    setDateRange({ startDate: '', endDate: '' })
    loadStatistics()
  }

  const exportStatistics = () => {
    if (!stats) return

    const csvData = [
      ['تقرير الإحصائيات - نظام مرسال'],
      [''],
      ['الإحصائيات العامة'],
      ['إجمالي الطلبات', stats.totalOrders.toString()],
      ['طلبات في الانتظار', stats.pendingOrders.toString()],
      ['طلبات قيد المعالجة', stats.processingOrders.toString()],
      ['طلبات تم شحنها', stats.shippedOrders.toString()],
      ['طلبات في الطريق', stats.outForDeliveryOrders.toString()],
      ['طلبات مسلمة', stats.deliveredOrders.toString()],
      ['طلبات مرجعة', stats.returnedOrders.toString()],
      ['طلبات ملغية', stats.cancelledOrders.toString()],
      ['متوسط قيمة الطلب', formatCurrency(stats.averageOrderValue)],
      ['الطلبات في اليوم', stats.ordersPerDay.toFixed(1)],
      [''],
      ['الاتجاهات'],
      ['هذا الأسبوع', stats.recentTrends.thisWeek.toString()],
      ['الأسبوع الماضي', stats.recentTrends.lastWeek.toString()],
      ['هذا الشهر', stats.recentTrends.thisMonth.toString()],
      ['الشهر الماضي', stats.recentTrends.lastMonth.toString()],
      [''],
      ['توزيع الحالات'],
      ['الحالة', 'العدد', 'النسبة المئوية'],
      ...stats.statusDistribution.map(item => [
        item.label,
        item.count.toString(),
        `${item.percentage.toFixed(1)}%`
      ])
    ]

    const csvContent = csvData.map(row => row.join(',')).join('\n')
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `إحصائيات-${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  if (isLoading || isLoadingStats) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!user || !stats) return null

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="flex">
        <Navigation />
        
        <main className="flex-1 p-6">
          {/* العنوان والإجراءات */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">الإحصائيات والتقارير</h1>
              <p className="text-gray-600 mt-1">
                تحليل شامل لأداء النظام والطلبات
              </p>
            </div>
            
            <Button
              onClick={exportStatistics}
              className="flex items-center space-x-2 space-x-reverse"
            >
              <Download className="h-4 w-4" />
              <span>تصدير التقرير</span>
            </Button>
          </div>

          {/* فلتر التاريخ */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 space-x-reverse">
                <Calendar className="h-5 w-5" />
                <span>فترة التقرير</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row gap-4 items-end">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
                  <input
                    type="date"
                    value={dateRange.startDate}
                    onChange={(e) => handleDateRangeChange('startDate', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
                  <input
                    type="date"
                    value={dateRange.endDate}
                    onChange={(e) => handleDateRangeChange('endDate', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <Button
                  variant="outline"
                  onClick={clearDateRange}
                  disabled={!dateRange.startDate && !dateRange.endDate}
                >
                  مسح الفلتر
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* الإحصائيات الرئيسية */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <Package className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">إجمالي الطلبات</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalOrders}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="p-3 bg-green-100 rounded-lg">
                    <Target className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">طلبات مسلمة</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.deliveredOrders}</p>
                    <p className="text-xs text-green-600">
                      {stats.totalOrders > 0 ? ((stats.deliveredOrders / stats.totalOrders) * 100).toFixed(1) : 0}%
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="p-3 bg-yellow-100 rounded-lg">
                    <Clock className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">متوسط قيمة الطلب</p>
                    <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.averageOrderValue)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="p-3 bg-purple-100 rounded-lg">
                    <TrendingUp className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">طلبات في اليوم</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.ordersPerDay.toFixed(1)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* الاتجاهات الحديثة */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 space-x-reverse">
                  <TrendingUp className="h-5 w-5" />
                  <span>الاتجاهات الحديثة</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">هذا الأسبوع</span>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <span className="font-semibold">{stats.recentTrends.thisWeek}</span>
                      {stats.recentTrends.lastWeek > 0 && (
                        <span className={`text-xs px-2 py-1 rounded ${
                          stats.recentTrends.thisWeek >= stats.recentTrends.lastWeek
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {stats.recentTrends.thisWeek >= stats.recentTrends.lastWeek ? '+' : ''}
                          {((stats.recentTrends.thisWeek - stats.recentTrends.lastWeek) / stats.recentTrends.lastWeek * 100).toFixed(1)}%
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">الأسبوع الماضي</span>
                    <span className="font-semibold">{stats.recentTrends.lastWeek}</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">هذا الشهر</span>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <span className="font-semibold">{stats.recentTrends.thisMonth}</span>
                      {stats.recentTrends.lastMonth > 0 && (
                        <span className={`text-xs px-2 py-1 rounded ${
                          stats.recentTrends.thisMonth >= stats.recentTrends.lastMonth
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {stats.recentTrends.thisMonth >= stats.recentTrends.lastMonth ? '+' : ''}
                          {((stats.recentTrends.thisMonth - stats.recentTrends.lastMonth) / stats.recentTrends.lastMonth * 100).toFixed(1)}%
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">الشهر الماضي</span>
                    <span className="font-semibold">{stats.recentTrends.lastMonth}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* أفضل مندوب */}
            {stats.topCourier && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2 space-x-reverse">
                    <Users className="h-5 w-5" />
                    <span>أفضل مندوب</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="h-8 w-8 text-blue-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">{stats.topCourier.name}</h3>
                    <p className="text-sm text-gray-600">
                      {stats.topCourier.orders} طلب في الفترة المحددة
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* توزيع الحالات */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 space-x-reverse">
                <BarChart3 className="h-5 w-5" />
                <span>توزيع حالات الطلبات</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.statusDistribution.map((item) => (
                  <div key={item.status} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <span className="text-sm font-medium text-gray-900">{item.label}</span>
                      <span className="text-xs text-gray-600">({item.count})</span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${item.percentage}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium text-gray-900 w-12 text-left">
                        {item.percentage.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  )
}
