'use client'

import React from 'react'
import { MapPin, Users, Package, TrendingUp, Phone, Clock } from 'lucide-react'
import { Card, CardContent, CardHeader } from '../ui/card'
import { Badge } from '../ui/badge'

interface BranchCardProps {
  id: string
  name: string
  address: string
  phone: string
  manager: string
  totalOrders: number
  activeOrders: number
  completedOrders: number
  employees: number
  status: 'active' | 'inactive' | 'maintenance'
  workingHours: string
  performance: number
}

export function BranchCard({
  id,
  name,
  address,
  phone,
  manager,
  totalOrders,
  activeOrders,
  completedOrders,
  employees,
  status,
  workingHours,
  performance
}: BranchCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'نشط'
      case 'inactive':
        return 'غير نشط'
      case 'maintenance':
        return 'صيانة'
      default:
        return status
    }
  }

  const getPerformanceColor = (performance: number) => {
    if (performance >= 90) return 'text-green-600'
    if (performance >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  return (
    <Card className="group cursor-pointer transition-all duration-300 hover-lift border-2 border-gray-200 hover:border-blue-300 bg-white hover:shadow-strong animate-fade-in">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 space-x-reverse mb-2">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                <MapPin className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                  {name}
                </h3>
                <p className="text-sm text-gray-500">#{id}</p>
              </div>
            </div>
            
            <Badge className={`${getStatusColor(status)} border`}>
              {getStatusText(status)}
            </Badge>
          </div>
          
          <div className="text-left">
            <div className={`text-2xl font-bold ${getPerformanceColor(performance)}`}>
              {performance}%
            </div>
            <p className="text-xs text-gray-500">الأداء</p>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-4">
          {/* معلومات الفرع */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
              <MapPin className="h-4 w-4" />
              <span>{address}</span>
            </div>
            
            <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
              <Phone className="h-4 w-4" />
              <span>{phone}</span>
            </div>
            
            <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
              <Clock className="h-4 w-4" />
              <span>{workingHours}</span>
            </div>
            
            <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
              <Users className="h-4 w-4" />
              <span>المدير: {manager}</span>
            </div>
          </div>

          {/* إحصائيات الطلبات */}
          <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-100">
            <div className="text-center">
              <div className="flex items-center justify-center space-x-1 space-x-reverse mb-1">
                <Package className="h-4 w-4 text-blue-500" />
                <span className="text-lg font-bold text-blue-600">{totalOrders}</span>
              </div>
              <p className="text-xs text-gray-500">إجمالي الطلبات</p>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center space-x-1 space-x-reverse mb-1">
                <TrendingUp className="h-4 w-4 text-green-500" />
                <span className="text-lg font-bold text-green-600">{completedOrders}</span>
              </div>
              <p className="text-xs text-gray-500">مكتملة</p>
            </div>
          </div>

          {/* شريط التقدم */}
          <div className="pt-2">
            <div className="flex justify-between text-xs text-gray-500 mb-1">
              <span>الطلبات النشطة</span>
              <span>{activeOrders} من {totalOrders}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-500"
                style={{ width: `${totalOrders > 0 ? (activeOrders / totalOrders) * 100 : 0}%` }}
              ></div>
            </div>
          </div>

          {/* عدد الموظفين */}
          <div className="flex items-center justify-between pt-2 border-t border-gray-100">
            <span className="text-sm text-gray-600">عدد الموظفين</span>
            <div className="flex items-center space-x-1 space-x-reverse">
              <Users className="h-4 w-4 text-gray-400" />
              <span className="font-semibold text-gray-900">{employees}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
