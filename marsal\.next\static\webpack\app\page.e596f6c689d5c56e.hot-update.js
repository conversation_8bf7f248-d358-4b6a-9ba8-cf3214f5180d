"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/cards/section-card.tsx":
/*!***********************************************!*\
  !*** ./src/components/cards/section-card.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SectionCard: () => (/* binding */ SectionCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ SectionCard auto */ \n\n\n\nconst colorClasses = {\n    blue: {\n        gradient: 'from-blue-500 to-blue-600',\n        bg: 'bg-blue-50',\n        text: 'text-blue-600',\n        border: 'border-blue-200',\n        hover: 'hover:border-blue-300'\n    },\n    green: {\n        gradient: 'from-green-500 to-green-600',\n        bg: 'bg-green-50',\n        text: 'text-green-600',\n        border: 'border-green-200',\n        hover: 'hover:border-green-300'\n    },\n    purple: {\n        gradient: 'from-purple-500 to-purple-600',\n        bg: 'bg-purple-50',\n        text: 'text-purple-600',\n        border: 'border-purple-200',\n        hover: 'hover:border-purple-300'\n    },\n    orange: {\n        gradient: 'from-orange-500 to-orange-600',\n        bg: 'bg-orange-50',\n        text: 'text-orange-600',\n        border: 'border-orange-200',\n        hover: 'hover:border-orange-300'\n    },\n    red: {\n        gradient: 'from-red-500 to-red-600',\n        bg: 'bg-red-50',\n        text: 'text-red-600',\n        border: 'border-red-200',\n        hover: 'hover:border-red-300'\n    },\n    indigo: {\n        gradient: 'from-indigo-500 to-indigo-600',\n        bg: 'bg-indigo-50',\n        text: 'text-indigo-600',\n        border: 'border-indigo-200',\n        hover: 'hover:border-indigo-300'\n    },\n    pink: {\n        gradient: 'from-pink-500 to-pink-600',\n        bg: 'bg-pink-50',\n        text: 'text-pink-600',\n        border: 'border-pink-200',\n        hover: 'hover:border-pink-300'\n    },\n    teal: {\n        gradient: 'from-teal-500 to-teal-600',\n        bg: 'bg-teal-50',\n        text: 'text-teal-600',\n        border: 'border-teal-200',\n        hover: 'hover:border-teal-300'\n    },\n    gray: {\n        gradient: 'from-gray-500 to-gray-600',\n        bg: 'bg-gray-50',\n        text: 'text-gray-600',\n        border: 'border-gray-200',\n        hover: 'hover:border-gray-300'\n    }\n};\nfunction SectionCard(param) {\n    let { title, description, icon: Icon, href, color, count, trend } = param;\n    const colors = colorClasses[color] || colorClasses.blue // fallback to blue if color not found\n    ;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n        href: href,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"group cursor-pointer transition-all duration-300 hover-lift border-2 \".concat(colors.border, \" \").concat(colors.hover, \" \").concat(colors.bg, \" hover:shadow-strong animate-fade-in\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 space-x-reverse mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 rounded-xl bg-gradient-to-br \".concat(colors.gradient, \" flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold \".concat(colors.text, \" group-hover:text-opacity-80 transition-colors\"),\n                                                children: title\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 19\n                                            }, this),\n                                            count !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 space-x-reverse mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl font-bold \".concat(colors.text),\n                                                        children: count.toLocaleString('ar-EG')\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium \".concat(trend.isPositive ? 'text-green-600' : 'text-red-600'),\n                                                        children: [\n                                                            trend.isPositive ? '+' : '',\n                                                            trend.value,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 text-sm leading-relaxed mb-4\",\n                                children: description\n                            }, void 0, false, {\n                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center text-sm font-medium \".concat(colors.text, \" group-hover:translate-x-1 transition-transform duration-200\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"عرض التفاصيل\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M15 19l-7-7 7-7\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                lineNumber: 93,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n_c = SectionCard;\nvar _c;\n$RefreshReg$(_c, \"SectionCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/cards/section-card.tsx\n"));

/***/ })

});