# مرسال - نظام إدارة التوصيل

نظام شامل لإدارة التوصيل مصمم خصيصاً للسوق العراقي مع دعم متعدد المنصات.

## المميزات

- **واجهة عربية كاملة** مع دعم RTL
- **ثلاثة أدوار مستخدمين**: مدير، متابع، مندوب
- **إدارة شاملة للطلبات** مع تتبع الحالات
- **نظام إدارة الموظفين** مع صلاحيات محددة
- **تقارير مالية مفصلة** وإحصائيات الأداء
- **نظام الإرجاع والاستبدال**
- **استيراد وتصدير البيانات**
- **دعم متعدد المنصات**: ويب، أندرويد، iOS، ويندوز

## التقنيات المستخدمة

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS, ShadCN UI
- **Mobile**: Capacitor (Android & iOS)
- **Desktop**: Electron (Windows, macOS, Linux)
- **Database**: localStorage (للتطوير) - سيتم الترقية إلى Firebase/Firestore

## التشغيل والتطوير

### متطلبات النظام

- Node.js 18+ 
- npm أو yarn
- Android Studio (للتطوير على أندرويد)
- Xcode (للتطوير على iOS - macOS فقط)

### التثبيت

```bash
# استنساخ المشروع
git clone [repository-url]
cd marsal

# تثبيت التبعيات
npm install

# تشغيل خادم التطوير
npm run dev
```

افتح [http://localhost:3000](http://localhost:3000) في المتصفح لرؤية التطبيق.

### بيانات تسجيل الدخول الافتراضية

**مدير:**
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

**متابع:**
- اسم المستخدم: `supervisor`
- كلمة المرور: `supervisor123`

**مندوب:**
- اسم المستخدم: `courier`
- كلمة المرور: `courier123`

## البناء والنشر

### تطبيق الويب

```bash
# بناء التطبيق للإنتاج
npm run build

# تصدير ملفات ثابتة
npm run export
```

### تطبيق الأندرويد

```bash
# بناء ومزامنة مع Capacitor
npm run cap:build

# فتح في Android Studio
npm run cap:android
```

### تطبيق iOS

```bash
# بناء ومزامنة مع Capacitor
npm run cap:build

# فتح في Xcode
npm run cap:ios
```

### تطبيق سطح المكتب (Electron)

```bash
# تشغيل في وضع التطوير
npm run electron:dev

# بناء للإنتاج
npm run electron:build
```

## هيكل المشروع

```
marsal/
├── src/
│   ├── app/                 # صفحات التطبيق (App Router)
│   │   ├── accounting/      # المحاسبة والتقارير المالية
│   │   ├── dispatch/        # إدارة التوزيع
│   │   ├── employees/       # إدارة الموظفين
│   │   ├── import-export/   # استيراد وتصدير البيانات
│   │   ├── orders/          # إدارة الطلبات
│   │   ├── returns/         # إدارة المرتجعات
│   │   └── statistics/      # الإحصائيات والتقارير
│   ├── components/          # مكونات React قابلة للإعادة
│   │   ├── ui/             # مكونات واجهة المستخدم الأساسية
│   │   ├── auth-provider.tsx
│   │   ├── header.tsx
│   │   └── navigation.tsx
│   ├── lib/                # وظائف مساعدة ومرافق
│   │   ├── storage.ts      # إدارة البيانات المحلية
│   │   └── utils.ts        # وظائف مساعدة عامة
│   └── types/              # تعريفات TypeScript
├── public/                 # ملفات عامة
├── android/               # مشروع Android (Capacitor)
├── ios/                   # مشروع iOS (Capacitor)
├── electron.js            # تطبيق Electron الرئيسي
└── capacitor.config.ts    # إعدادات Capacitor
```

## الأدوار والصلاحيات

### المدير
- الوصول الكامل لجميع الأقسام
- إدارة الموظفين (إضافة، تعديل، حذف)
- التقارير المالية والإحصائيات
- إدارة التوزيع والإرجاع
- استيراد وتصدير البيانات

### المتابع
- عرض وإدارة الطلبات
- إضافة مندوبين جدد فقط
- عرض إحصائيات محدودة
- إدارة التوزيع للمندوبين

### المندوب
- عرض الطلبات المخصصة له فقط
- تحديث حالة الطلبات
- عرض إحصائياته الشخصية

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى الفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى فتح issue في GitHub أو التواصل معنا عبر البريد الإلكتروني.
