'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/auth-provider'
import { Header } from '@/components/header'
import { Navigation } from '@/components/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { getOrders, searchOrders, updateOrderStatus } from '@/lib/storage'
import { Order, ReturnReason } from '@/types'
import { Search, RotateCcw, Eye, Edit, Calendar, DollarSign, Package, AlertTriangle } from 'lucide-react'
import { formatCurrency, formatDate } from '@/lib/utils'

export default function ReturnsPage() {
  const { user, isLoading } = useAuth()
  const router = useRouter()
  const [returnedOrders, setReturnedOrders] = useState<Order[]>([])
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [reasonFilter, setReasonFilter] = useState<ReturnReason | 'all'>('all')
  const [isLoadingOrders, setIsLoadingOrders] = useState(true)

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/login')
      return
    }

    // التحقق من الصلاحيات - فقط المدير يمكنه الوصول
    if (user && user.role !== 'manager') {
      router.push('/')
      return
    }

    if (user) {
      loadReturnedOrders()
    }
  }, [user, isLoading, router])

  const loadReturnedOrders = () => {
    setIsLoadingOrders(true)
    try {
      const allOrders = getOrders()
      const returned = allOrders.filter(order => order.status === 'returned')
      setReturnedOrders(returned)
      setFilteredOrders(returned)
    } catch (error) {
      console.error('Error loading returned orders:', error)
    } finally {
      setIsLoadingOrders(false)
    }
  }

  useEffect(() => {
    let filtered = returnedOrders

    // تطبيق البحث
    if (searchQuery.trim()) {
      filtered = searchOrders(searchQuery).filter(order => order.status === 'returned')
    }

    // تطبيق فلتر سبب الإرجاع
    if (reasonFilter !== 'all') {
      filtered = filtered.filter(order => {
        const latestStatus = order.statusHistory[0]
        return latestStatus && latestStatus.returnReason === reasonFilter
      })
    }

    setFilteredOrders(filtered)
  }, [searchQuery, reasonFilter, returnedOrders])

  const getReturnReason = (order: Order): ReturnReason | undefined => {
    const latestStatus = order.statusHistory.find(status => status.status === 'returned')
    return latestStatus?.returnReason
  }

  const getReturnReasonLabel = (reason: ReturnReason | undefined) => {
    if (!reason) return 'غير محدد'
    
    const labels = {
      customer_refused: 'رفض العميل',
      wrong_address: 'عنوان خاطئ',
      customer_not_available: 'العميل غير متوفر',
      damaged_item: 'البضاعة تالفة',
      wrong_item: 'بضاعة خاطئة',
      payment_issue: 'مشكلة في الدفع',
      other: 'أخرى'
    }
    return labels[reason] || reason
  }

  const getReasonColor = (reason: ReturnReason | undefined) => {
    if (!reason) return 'bg-gray-100 text-gray-800'
    
    const colors = {
      customer_refused: 'bg-red-100 text-red-800',
      wrong_address: 'bg-orange-100 text-orange-800',
      customer_not_available: 'bg-yellow-100 text-yellow-800',
      damaged_item: 'bg-purple-100 text-purple-800',
      wrong_item: 'bg-blue-100 text-blue-800',
      payment_issue: 'bg-pink-100 text-pink-800',
      other: 'bg-gray-100 text-gray-800'
    }
    return colors[reason] || 'bg-gray-100 text-gray-800'
  }

  const handleReprocess = async (orderId: string) => {
    const order = returnedOrders.find(o => o.id === orderId)
    if (!order) return

    const confirmMessage = `هل أنت متأكد من إعادة معالجة الطلب ${order.trackingNumber}؟`
    
    if (window.confirm(confirmMessage)) {
      try {
        const success = updateOrderStatus(
          orderId,
          'processing',
          'تم إعادة معالجة الطلب المرجع',
          undefined,
          undefined,
          undefined,
          undefined,
          user?.name || 'النظام'
        )

        if (success) {
          alert('تم إعادة معالجة الطلب بنجاح')
          loadReturnedOrders()
        } else {
          alert('حدث خطأ أثناء إعادة معالجة الطلب')
        }
      } catch (error) {
        console.error('Error reprocessing order:', error)
        alert('حدث خطأ أثناء إعادة معالجة الطلب')
      }
    }
  }

  const calculateReturnStats = () => {
    const totalReturns = filteredOrders.length
    const totalAmount = filteredOrders.reduce((sum, order) => sum + order.amount, 0)
    
    const reasonCounts = filteredOrders.reduce((acc, order) => {
      const reason = getReturnReason(order) || 'other'
      acc[reason] = (acc[reason] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return { totalReturns, totalAmount, reasonCounts }
  }

  const stats = calculateReturnStats()

  if (isLoading || isLoadingOrders) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!user || user.role !== 'manager') return null

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="flex">
        <Navigation />
        
        <main className="flex-1 p-6">
          {/* العنوان */}
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">إدارة المرتجعات</h1>
            <p className="text-gray-600 mt-1">
              إدارة ومتابعة الطلبات المرجعة
            </p>
          </div>

          {/* إحصائيات المرتجعات */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="p-3 bg-red-100 rounded-lg">
                    <RotateCcw className="h-6 w-6 text-red-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">إجمالي المرتجعات</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalReturns}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="p-3 bg-orange-100 rounded-lg">
                    <DollarSign className="h-6 w-6 text-orange-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">قيمة المرتجعات</p>
                    <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.totalAmount)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="p-3 bg-yellow-100 rounded-lg">
                    <AlertTriangle className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">السبب الأكثر شيوعاً</p>
                    <p className="text-lg font-bold text-gray-900">
                      {Object.keys(stats.reasonCounts).length > 0 
                        ? getReturnReasonLabel(Object.keys(stats.reasonCounts).reduce((a, b) => 
                            stats.reasonCounts[a] > stats.reasonCounts[b] ? a : b
                          ) as ReturnReason)
                        : 'لا يوجد'
                      }
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* شريط البحث والفلاتر */}
          <Card className="mb-6">
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row gap-4">
                {/* البحث */}
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="البحث برقم التتبع أو اسم العميل أو رقم الهاتف..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pr-10"
                    />
                  </div>
                </div>

                {/* فلتر سبب الإرجاع */}
                <div className="md:w-64">
                  <select
                    value={reasonFilter}
                    onChange={(e) => setReasonFilter(e.target.value as ReturnReason | 'all')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">جميع الأسباب</option>
                    <option value="customer_refused">رفض العميل</option>
                    <option value="wrong_address">عنوان خاطئ</option>
                    <option value="customer_not_available">العميل غير متوفر</option>
                    <option value="damaged_item">البضاعة تالفة</option>
                    <option value="wrong_item">بضاعة خاطئة</option>
                    <option value="payment_issue">مشكلة في الدفع</option>
                    <option value="other">أخرى</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* قائمة المرتجعات */}
          {filteredOrders.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="text-gray-400 mb-4">
                  <RotateCcw className="h-16 w-16 mx-auto" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مرتجعات</h3>
                <p className="text-gray-600">
                  {searchQuery || reasonFilter !== 'all' 
                    ? 'لم يتم العثور على مرتجعات تطابق معايير البحث'
                    : 'لا توجد طلبات مرجعة حالياً'
                  }
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {filteredOrders.map((order) => {
                const returnReason = getReturnReason(order)
                return (
                  <Card key={order.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center space-x-4 space-x-reverse mb-3">
                            <h3 className="text-lg font-semibold text-gray-900">
                              {order.trackingNumber}
                            </h3>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getReasonColor(returnReason)}`}>
                              {getReturnReasonLabel(returnReason)}
                            </span>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600">
                            <div>
                              <p><strong>المستلم:</strong> {order.recipientName}</p>
                              <p><strong>الهاتف:</strong> {order.recipientPhone}</p>
                            </div>
                            <div>
                              <p><strong>المندوب:</strong> {order.courierName || 'غير محدد'}</p>
                              <p><strong>المبلغ:</strong> {formatCurrency(order.amount)}</p>
                            </div>
                            <div>
                              <div className="flex items-center space-x-2 space-x-reverse">
                                <Calendar className="h-4 w-4" />
                                <span>تاريخ الإرجاع: {formatDate(order.updatedAt)}</span>
                              </div>
                            </div>
                            <div>
                              {order.statusHistory[0]?.returnedPieces && (
                                <p><strong>القطع المرجعة:</strong> {order.statusHistory[0].returnedPieces}</p>
                              )}
                              {order.statusHistory[0]?.newAmount && (
                                <p><strong>المبلغ الجديد:</strong> {formatCurrency(order.statusHistory[0].newAmount)}</p>
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="flex space-x-2 space-x-reverse">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.push(`/orders/${order.id}`)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleReprocess(order.id)}
                            className="text-green-600 hover:text-green-700 hover:bg-green-50"
                          >
                            <RotateCcw className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}
        </main>
      </div>
    </div>
  )
}
