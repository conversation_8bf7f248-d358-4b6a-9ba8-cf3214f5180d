'use client'

import React from 'react'
import { useAuth } from './auth-provider'
import { Button } from './ui/button'
import { LogOut, User, Bell, Search } from 'lucide-react'
import { Input } from './ui/input'

export function Header() {
  const { user, logout } = useAuth()

  const handleLogout = () => {
    logout()
    window.location.href = '/login'
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'manager':
        return 'bg-gradient-to-r from-purple-500 to-purple-600 text-white'
      case 'supervisor':
        return 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
      case 'courier':
        return 'bg-gradient-to-r from-green-500 to-green-600 text-white'
      default:
        return 'bg-gray-100 text-gray-700'
    }
  }

  return (
    <header className="glass-effect border-b border-white/20 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* الشعار */}
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="w-12 h-12 gradient-primary rounded-2xl flex items-center justify-center shadow-lg hover:scale-110 transition-transform duration-300">
              <span className="text-white font-bold text-xl">م</span>
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gradient">مرسال</h1>
              <p className="text-sm text-gray-600 -mt-1">نظام إدارة التوصيل المتطور</p>
            </div>
          </div>

          {/* شريط البحث */}
          <div className="flex-1 max-w-xl mx-8">
            <div className="relative">
              <div className="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="بحث سريع برقم التتبع أو اسم العميل..."
                className="w-full pr-12 pl-6 py-3 glass-effect rounded-2xl border border-white/30 backdrop-blur-sm text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-300 hover:bg-white/60"
              />
            </div>
          </div>

          {/* معلومات المستخدم والإجراءات */}
          <div className="flex items-center space-x-4 space-x-reverse">
            {/* الإشعارات */}
            <Button variant="ghost" size="icon" className="relative glass-effect hover:bg-white/30 transition-all duration-300 rounded-xl">
              <Bell className="h-5 w-5 text-gray-700" />
              <span className="absolute -top-1 -right-1 h-5 w-5 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full flex items-center justify-center shadow-lg animate-pulse">
                3
              </span>
            </Button>

            {/* معلومات المستخدم */}
            <div className="flex items-center space-x-4 space-x-reverse glass-effect px-4 py-2 rounded-2xl hover:bg-white/30 transition-all duration-300">
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className={`w-10 h-10 rounded-xl flex items-center justify-center shadow-lg ${getRoleColor(user?.role || '')}`}>
                  <User className="h-5 w-5" />
                </div>
                <div className="text-sm">
                  <div className="font-bold text-gray-900">{user?.name}</div>
                  <div className="text-gray-600 font-medium">
                    {user?.role === 'manager' && 'مدير النظام'}
                    {user?.role === 'supervisor' && 'متابع العمليات'}
                    {user?.role === 'courier' && 'مندوب التوصيل'}
                  </div>
                </div>
              </div>

              {/* زر تسجيل الخروج */}
              <Button
                variant="ghost"
                size="icon"
                onClick={handleLogout}
                className="text-red-500 hover:text-red-600 hover:bg-red-50 transition-all duration-300 rounded-xl"
                title="تسجيل الخروج"
              >
                <LogOut className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}
