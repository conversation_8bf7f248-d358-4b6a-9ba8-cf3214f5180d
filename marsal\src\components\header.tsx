'use client'

import React from 'react'
import { useAuth } from './auth-provider'
import { Button } from './ui/button'
import { LogOut, User, Bell, Search } from 'lucide-react'
import { Input } from './ui/input'

export function Header() {
  const { user, logout } = useAuth()

  const handleLogout = () => {
    logout()
    window.location.href = '/login'
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'manager':
        return 'bg-gradient-to-r from-purple-500 to-purple-600 text-white'
      case 'supervisor':
        return 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
      case 'courier':
        return 'bg-gradient-to-r from-green-500 to-green-600 text-white'
      default:
        return 'bg-gray-100 text-gray-700'
    }
  }

  return (
    <header className="glass-effect border-b border-white/20 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* الشعار */}
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="w-10 h-10 gradient-primary rounded-xl flex items-center justify-center shadow-lg animate-pulse-slow">
              <span className="text-white font-bold text-lg">م</span>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gradient">مرسال</h1>
              <p className="text-xs text-gray-500 -mt-1">نظام إدارة التوصيل المتطور</p>
            </div>
          </div>

          {/* شريط البحث */}
          <div className="flex-1 max-w-lg mx-8">
            <div className="relative">
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="بحث سريع برقم التتبع أو اسم العميل..."
                className="w-full pr-10 pl-4 py-2 bg-white/50 rounded-full border border-white/20 backdrop-blur-sm text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
              />
            </div>
          </div>

          {/* معلومات المستخدم والإجراءات */}
          <div className="flex items-center space-x-4 space-x-reverse">
            {/* الإشعارات */}
            <Button variant="ghost" size="icon" className="relative">
              <Bell className="h-5 w-5" />
              <span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white"></span>
            </Button>

            {/* معلومات المستخدم */}
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="flex items-center space-x-2 space-x-reverse">
                <User className="h-5 w-5 text-gray-400" />
                <div className="text-sm">
                  <div className="font-medium text-gray-900">{user?.name}</div>
                  <div className="text-gray-500">
                    {user?.role === 'manager' && 'مدير'}
                    {user?.role === 'supervisor' && 'متابع'}
                    {user?.role === 'courier' && 'مندوب'}
                  </div>
                </div>
              </div>

              {/* زر تسجيل الخروج */}
              <Button
                variant="ghost"
                size="icon"
                onClick={handleLogout}
                className="text-gray-400 hover:text-gray-600"
              >
                <LogOut className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}
