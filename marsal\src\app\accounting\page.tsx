'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/auth-provider'
import { Header } from '@/components/header'
import { Navigation } from '@/components/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { getOrders, getEmployees } from '@/lib/storage'
import { Order, User } from '@/types'
import { DollarSign, TrendingUp, TrendingDown, Users, Calendar, Download, Filter } from 'lucide-react'
import { formatCurrency, formatDate } from '@/lib/utils'

interface AccountingStats {
  totalRevenue: number
  deliveredRevenue: number
  pendingRevenue: number
  returnedRevenue: number
  totalOrders: number
  deliveredOrders: number
  returnedOrders: number
  courierPerformance: Array<{
    courierId: string
    courierName: string
    totalOrders: number
    deliveredOrders: number
    returnedOrders: number
    totalRevenue: number
    deliveredRevenue: number
    successRate: number
  }>
}

export default function AccountingPage() {
  const { user, isLoading } = useAuth()
  const router = useRouter()
  const [orders, setOrders] = useState<Order[]>([])
  const [couriers, setCouriers] = useState<User[]>([])
  const [stats, setStats] = useState<AccountingStats | null>(null)
  const [dateFilter, setDateFilter] = useState({
    startDate: '',
    endDate: ''
  })
  const [isLoadingData, setIsLoadingData] = useState(true)

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/login')
      return
    }

    // التحقق من الصلاحيات - فقط المدير يمكنه الوصول
    if (user && user.role !== 'manager') {
      router.push('/')
      return
    }

    if (user) {
      loadData()
    }
  }, [user, isLoading, router])

  const loadData = () => {
    setIsLoadingData(true)
    try {
      const allOrders = getOrders()
      const allCouriers = getEmployees().filter(emp => emp.role === 'courier')
      
      setOrders(allOrders)
      setCouriers(allCouriers)
      calculateStats(allOrders, allCouriers)
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setIsLoadingData(false)
    }
  }

  const calculateStats = (ordersData: Order[], couriersData: User[]) => {
    // تطبيق فلتر التاريخ
    let filteredOrders = ordersData
    if (dateFilter.startDate && dateFilter.endDate) {
      const startDate = new Date(dateFilter.startDate)
      const endDate = new Date(dateFilter.endDate)
      endDate.setHours(23, 59, 59, 999) // نهاية اليوم
      
      filteredOrders = ordersData.filter(order => {
        const orderDate = new Date(order.createdAt)
        return orderDate >= startDate && orderDate <= endDate
      })
    }

    // حساب الإحصائيات العامة
    const totalRevenue = filteredOrders.reduce((sum, order) => sum + order.amount, 0)
    const deliveredOrders = filteredOrders.filter(order => order.status === 'delivered')
    const returnedOrders = filteredOrders.filter(order => order.status === 'returned')
    const pendingOrders = filteredOrders.filter(order => 
      ['pending', 'processing', 'shipped', 'out_for_delivery'].includes(order.status)
    )

    const deliveredRevenue = deliveredOrders.reduce((sum, order) => sum + order.amount, 0)
    const returnedRevenue = returnedOrders.reduce((sum, order) => sum + order.amount, 0)
    const pendingRevenue = pendingOrders.reduce((sum, order) => sum + order.amount, 0)

    // حساب أداء المندوبين
    const courierPerformance = couriersData.map(courier => {
      const courierOrders = filteredOrders.filter(order => order.courierId === courier.id)
      const courierDelivered = courierOrders.filter(order => order.status === 'delivered')
      const courierReturned = courierOrders.filter(order => order.status === 'returned')
      
      const totalRevenue = courierOrders.reduce((sum, order) => sum + order.amount, 0)
      const deliveredRevenue = courierDelivered.reduce((sum, order) => sum + order.amount, 0)
      const successRate = courierOrders.length > 0 
        ? (courierDelivered.length / courierOrders.length) * 100 
        : 0

      return {
        courierId: courier.id,
        courierName: courier.name,
        totalOrders: courierOrders.length,
        deliveredOrders: courierDelivered.length,
        returnedOrders: courierReturned.length,
        totalRevenue,
        deliveredRevenue,
        successRate
      }
    }).filter(performance => performance.totalOrders > 0)
    .sort((a, b) => b.deliveredRevenue - a.deliveredRevenue)

    const accountingStats: AccountingStats = {
      totalRevenue,
      deliveredRevenue,
      pendingRevenue,
      returnedRevenue,
      totalOrders: filteredOrders.length,
      deliveredOrders: deliveredOrders.length,
      returnedOrders: returnedOrders.length,
      courierPerformance
    }

    setStats(accountingStats)
  }

  const handleDateFilterChange = (field: string, value: string) => {
    const newFilter = { ...dateFilter, [field]: value }
    setDateFilter(newFilter)
    
    if (newFilter.startDate && newFilter.endDate) {
      calculateStats(orders, couriers)
    }
  }

  const clearDateFilter = () => {
    setDateFilter({ startDate: '', endDate: '' })
    calculateStats(orders, couriers)
  }

  const exportToCSV = () => {
    if (!stats) return

    const csvData = [
      ['التقرير المالي - نظام مرسال'],
      [''],
      ['الإحصائيات العامة'],
      ['إجمالي الإيرادات', formatCurrency(stats.totalRevenue)],
      ['إيرادات مسلمة', formatCurrency(stats.deliveredRevenue)],
      ['إيرادات معلقة', formatCurrency(stats.pendingRevenue)],
      ['إيرادات مرجعة', formatCurrency(stats.returnedRevenue)],
      ['إجمالي الطلبات', stats.totalOrders.toString()],
      ['طلبات مسلمة', stats.deliveredOrders.toString()],
      ['طلبات مرجعة', stats.returnedOrders.toString()],
      [''],
      ['أداء المندوبين'],
      ['اسم المندوب', 'إجمالي الطلبات', 'طلبات مسلمة', 'طلبات مرجعة', 'إجمالي الإيرادات', 'إيرادات مسلمة', 'معدل النجاح'],
      ...stats.courierPerformance.map(performance => [
        performance.courierName,
        performance.totalOrders.toString(),
        performance.deliveredOrders.toString(),
        performance.returnedOrders.toString(),
        formatCurrency(performance.totalRevenue),
        formatCurrency(performance.deliveredRevenue),
        `${performance.successRate.toFixed(1)}%`
      ])
    ]

    const csvContent = csvData.map(row => row.join(',')).join('\n')
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `تقرير-مالي-${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  if (isLoading || isLoadingData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!user || user.role !== 'manager' || !stats) return null

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="flex">
        <Navigation />
        
        <main className="flex-1 p-6">
          {/* العنوان والإجراءات */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">المحاسبة والتقارير</h1>
              <p className="text-gray-600 mt-1">التقارير المالية وأداء المندوبين</p>
            </div>
            
            <Button
              onClick={exportToCSV}
              className="flex items-center space-x-2 space-x-reverse"
            >
              <Download className="h-4 w-4" />
              <span>تصدير التقرير</span>
            </Button>
          </div>

          {/* فلتر التاريخ */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 space-x-reverse">
                <Filter className="h-5 w-5" />
                <span>فلتر التاريخ</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row gap-4 items-end">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
                  <Input
                    type="date"
                    value={dateFilter.startDate}
                    onChange={(e) => handleDateFilterChange('startDate', e.target.value)}
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
                  <Input
                    type="date"
                    value={dateFilter.endDate}
                    onChange={(e) => handleDateFilterChange('endDate', e.target.value)}
                  />
                </div>
                <Button
                  variant="outline"
                  onClick={clearDateFilter}
                  disabled={!dateFilter.startDate && !dateFilter.endDate}
                >
                  مسح الفلتر
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* الإحصائيات المالية */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <DollarSign className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">إجمالي الإيرادات</p>
                    <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.totalRevenue)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="p-3 bg-green-100 rounded-lg">
                    <TrendingUp className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">إيرادات مسلمة</p>
                    <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.deliveredRevenue)}</p>
                    <p className="text-xs text-green-600">
                      {stats.totalRevenue > 0 ? ((stats.deliveredRevenue / stats.totalRevenue) * 100).toFixed(1) : 0}%
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="p-3 bg-yellow-100 rounded-lg">
                    <Calendar className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">إيرادات معلقة</p>
                    <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.pendingRevenue)}</p>
                    <p className="text-xs text-yellow-600">
                      {stats.totalRevenue > 0 ? ((stats.pendingRevenue / stats.totalRevenue) * 100).toFixed(1) : 0}%
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="p-3 bg-red-100 rounded-lg">
                    <TrendingDown className="h-6 w-6 text-red-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">إيرادات مرجعة</p>
                    <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.returnedRevenue)}</p>
                    <p className="text-xs text-red-600">
                      {stats.totalRevenue > 0 ? ((stats.returnedRevenue / stats.totalRevenue) * 100).toFixed(1) : 0}%
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* أداء المندوبين */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 space-x-reverse">
                <Users className="h-5 w-5" />
                <span>أداء المندوبين</span>
              </CardTitle>
              <CardDescription>
                تفاصيل أداء كل مندوب وإحصائياته المالية
              </CardDescription>
            </CardHeader>
            <CardContent>
              {stats.courierPerformance.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">لا توجد بيانات أداء للمندوبين في الفترة المحددة</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-right py-3 px-4">اسم المندوب</th>
                        <th className="text-right py-3 px-4">إجمالي الطلبات</th>
                        <th className="text-right py-3 px-4">طلبات مسلمة</th>
                        <th className="text-right py-3 px-4">طلبات مرجعة</th>
                        <th className="text-right py-3 px-4">إجمالي الإيرادات</th>
                        <th className="text-right py-3 px-4">إيرادات مسلمة</th>
                        <th className="text-right py-3 px-4">معدل النجاح</th>
                      </tr>
                    </thead>
                    <tbody>
                      {stats.courierPerformance.map((performance) => (
                        <tr key={performance.courierId} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-4 font-medium">{performance.courierName}</td>
                          <td className="py-3 px-4">{performance.totalOrders}</td>
                          <td className="py-3 px-4 text-green-600">{performance.deliveredOrders}</td>
                          <td className="py-3 px-4 text-red-600">{performance.returnedOrders}</td>
                          <td className="py-3 px-4">{formatCurrency(performance.totalRevenue)}</td>
                          <td className="py-3 px-4 text-green-600">{formatCurrency(performance.deliveredRevenue)}</td>
                          <td className="py-3 px-4">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              performance.successRate >= 80 
                                ? 'bg-green-100 text-green-800'
                                : performance.successRate >= 60
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {performance.successRate.toFixed(1)}%
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  )
}
