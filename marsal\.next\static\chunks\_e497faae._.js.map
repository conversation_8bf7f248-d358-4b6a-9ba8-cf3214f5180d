{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// دوال مساعدة للتطبيق\nexport function formatDate(date: string | Date): string {\n  const d = new Date(date)\n  return d.toLocaleDateString('ar-IQ', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('ar-IQ', {\n    style: 'currency',\n    currency: 'IQD',\n    minimumFractionDigits: 0\n  }).format(amount)\n}\n\nexport function generateTrackingNumber(): string {\n  const prefix = 'MRS'\n  const timestamp = Date.now().toString().slice(-6)\n  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')\n  return `${prefix}${timestamp}${random}`\n}\n\nexport function validatePhoneNumber(phone: string): boolean {\n  // التحقق من أرقام الهواتف العراقية\n  const iraqiPhoneRegex = /^(\\+964|964|0)?(7[0-9]{9}|1[0-9]{9})$/\n  return iraqiPhoneRegex.test(phone.replace(/\\s/g, ''))\n}\n\nexport function validateIraqiPhone(phone: string): boolean {\n  return validatePhoneNumber(phone)\n}\n\nexport function copyToClipboard(text: string): Promise<void> {\n  return navigator.clipboard.writeText(text)\n}\n\nexport function shareViaWhatsApp(text: string): void {\n  const encodedText = encodeURIComponent(text)\n  const url = `https://wa.me/?text=${encodedText}`\n  window.open(url, '_blank')\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,QAAQ,KAAK;AACtB;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS;IACd,MAAM,SAAS;IACf,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC/C,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG;IACvE,OAAO,GAAG,SAAS,YAAY,QAAQ;AACzC;AAEO,SAAS,oBAAoB,KAAa;IAC/C,mCAAmC;IACnC,MAAM,kBAAkB;IACxB,OAAO,gBAAgB,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AACnD;AAEO,SAAS,mBAAmB,KAAa;IAC9C,OAAO,oBAAoB;AAC7B;AAEO,SAAS,gBAAgB,IAAY;IAC1C,OAAO,UAAU,SAAS,CAAC,SAAS,CAAC;AACvC;AAEO,SAAS,iBAAiB,IAAY;IAC3C,MAAM,cAAc,mBAAmB;IACvC,MAAM,MAAM,CAAC,oBAAoB,EAAE,aAAa;IAChD,OAAO,IAAI,CAAC,KAAK;AACnB", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;;AAIA;;;;;;AAEA,MAAM,iBAAiB,IACrB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,OAAO;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/src/components/header.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { useAuth } from './auth-provider'\nimport { Button } from './ui/button'\nimport { LogOut, User, Bell, Search } from 'lucide-react'\nimport { Input } from './ui/input'\n\nexport function Header() {\n  const { user, logout } = useAuth()\n\n  const handleLogout = () => {\n    logout()\n    window.location.href = '/login'\n  }\n\n  return (\n    <header className=\"bg-white border-b border-gray-200 shadow-sm\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* الشعار */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <h1 className=\"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                مرسال\n              </h1>\n            </div>\n          </div>\n\n          {/* شريط البحث */}\n          <div className=\"flex-1 max-w-lg mx-8\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n                <Search className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <Input\n                type=\"text\"\n                placeholder=\"البحث برقم التتبع أو اسم العميل...\"\n                className=\"block w-full pr-10 pl-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n              />\n            </div>\n          </div>\n\n          {/* معلومات المستخدم والإجراءات */}\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            {/* الإشعارات */}\n            <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n              <Bell className=\"h-5 w-5\" />\n              <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white\"></span>\n            </Button>\n\n            {/* معلومات المستخدم */}\n            <div className=\"flex items-center space-x-3 space-x-reverse\">\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <User className=\"h-5 w-5 text-gray-400\" />\n                <div className=\"text-sm\">\n                  <div className=\"font-medium text-gray-900\">{user?.name}</div>\n                  <div className=\"text-gray-500\">\n                    {user?.role === 'manager' && 'مدير'}\n                    {user?.role === 'supervisor' && 'متابع'}\n                    {user?.role === 'courier' && 'مندوب'}\n                  </div>\n                </div>\n              </div>\n\n              {/* زر تسجيل الخروج */}\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={handleLogout}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <LogOut className=\"h-5 w-5\" />\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;;;;;;AAEA;;;AANA;;;;;AAQO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD;IAE/B,MAAM,eAAe;QACnB;QACA,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;0CAAgG;;;;;;;;;;;;;;;;kCAOlH,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAO,WAAU;;;;;;;;;;;8CAEpB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;;;;;;;;;;;;kCAMhB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAO,WAAU;;kDAC5C,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;;;;;;;;;;;;0CAIlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;;;;;0DAChB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAA6B,MAAM;;;;;;kEAClD,6LAAC;wDAAI,WAAU;;4DACZ,MAAM,SAAS,aAAa;4DAC5B,MAAM,SAAS,gBAAgB;4DAC/B,MAAM,SAAS,aAAa;;;;;;;;;;;;;;;;;;;kDAMnC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC;4CAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlC;GAxEgB;;QACW,yIAAA,CAAA,UAAO;;;KADlB", "debugId": null}}, {"offset": {"line": 422, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/src/components/navigation.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useAuth } from './auth-provider'\nimport { cn } from '@/lib/utils'\nimport {\n  Home,\n  Package,\n  Users,\n  BarChart3,\n  Archive,\n  Settings,\n  Bell,\n  Truck,\n  RotateCcw,\n  Calculator,\n  Download,\n} from 'lucide-react'\n\ninterface NavigationItem {\n  name: string\n  href: string\n  icon: React.ComponentType<{ className?: string }>\n  roles: string[]\n}\n\nconst navigationItems: NavigationItem[] = [\n  {\n    name: 'الرئيسية',\n    href: '/',\n    icon: Home,\n    roles: ['manager', 'supervisor', 'courier'],\n  },\n  {\n    name: 'إدارة الطلبات',\n    href: '/orders',\n    icon: Package,\n    roles: ['manager', 'supervisor', 'courier'],\n  },\n  {\n    name: 'إسناد الطلبات',\n    href: '/dispatch',\n    icon: Truck,\n    roles: ['manager'],\n  },\n  {\n    name: 'إدارة الرواجع',\n    href: '/returns',\n    icon: RotateCcw,\n    roles: ['manager'],\n  },\n  {\n    name: 'المحاسبة',\n    href: '/accounting',\n    icon: Calculator,\n    roles: ['manager'],\n  },\n  {\n    name: 'الإحصائيات',\n    href: '/statistics',\n    icon: BarChart3,\n    roles: ['manager', 'supervisor', 'courier'],\n  },\n  {\n    name: 'الأرشيف',\n    href: '/archive',\n    icon: Archive,\n    roles: ['manager', 'supervisor', 'courier'],\n  },\n  {\n    name: 'إدارة الموظفين',\n    href: '/users',\n    icon: Users,\n    roles: ['manager', 'supervisor'],\n  },\n  {\n    name: 'استيراد وتصدير',\n    href: '/import-export',\n    icon: Download,\n    roles: ['manager'],\n  },\n  {\n    name: 'الإشعارات',\n    href: '/notifications',\n    icon: Bell,\n    roles: ['manager', 'supervisor', 'courier'],\n  },\n  {\n    name: 'الإعدادات',\n    href: '/settings',\n    icon: Settings,\n    roles: ['manager', 'supervisor', 'courier'],\n  },\n]\n\nexport function Navigation() {\n  const { user } = useAuth()\n  const pathname = usePathname()\n\n  if (!user) return null\n\n  // فلترة العناصر حسب دور المستخدم\n  const filteredItems = navigationItems.filter(item =>\n    item.roles.includes(user.role)\n  )\n\n  return (\n    <nav className=\"bg-white border-r border-gray-200 w-64 min-h-screen\">\n      <div className=\"p-4\">\n        <ul className=\"space-y-2\">\n          {filteredItems.map((item) => {\n            const Icon = item.icon\n            const isActive = pathname === item.href\n\n            return (\n              <li key={item.name}>\n                <Link\n                  href={item.href}\n                  className={cn(\n                    'flex items-center space-x-3 space-x-reverse px-3 py-2 rounded-md text-sm font-medium transition-colors',\n                    isActive\n                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'\n                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                  )}\n                >\n                  <Icon className=\"h-5 w-5\" />\n                  <span>{item.name}</span>\n                </Link>\n              </li>\n            )\n          })}\n        </ul>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;;;;;;;;AANA;;;;;;AA4BA,MAAM,kBAAoC;IACxC;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;YAAW;YAAc;SAAU;IAC7C;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;YAAW;YAAc;SAAU;IAC7C;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;SAAU;IACpB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;SAAU;IACpB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;SAAU;IACpB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;YAAW;YAAc;SAAU;IAC7C;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;YAAW;YAAc;SAAU;IAC7C;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;YAAW;SAAa;IAClC;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;SAAU;IACpB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;YAAW;YAAc;SAAU;IAC7C;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;YAAW;YAAc;SAAU;IAC7C;CACD;AAEM,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,IAAI,CAAC,MAAM,OAAO;IAElB,iCAAiC;IACjC,MAAM,gBAAgB,gBAAgB,MAAM,CAAC,CAAA,OAC3C,KAAK,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI;IAG/B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAG,WAAU;0BACX,cAAc,GAAG,CAAC,CAAC;oBAClB,MAAM,OAAO,KAAK,IAAI;oBACtB,MAAM,WAAW,aAAa,KAAK,IAAI;oBAEvC,qBACE,6LAAC;kCACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAM,KAAK,IAAI;4BACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA,WACI,wDACA;;8CAGN,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;8CAAM,KAAK,IAAI;;;;;;;;;;;;uBAXX,KAAK,IAAI;;;;;gBAetB;;;;;;;;;;;;;;;;AAKV;GAxCgB;;QACG,yIAAA,CAAA,UAAO;QACP,qIAAA,CAAA,cAAW;;;KAFd", "debugId": null}}, {"offset": {"line": 627, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 730, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/src/lib/storage.ts"], "sourcesContent": ["import { Order, OrderStatus, OrderStatusUpdate, Statistics, Notification } from '@/types'\nimport { generateTrackingNumber } from './utils'\n\n// مفاتيح التخزين المحلي\nconst STORAGE_KEYS = {\n  ORDERS: 'marsal_orders',\n  NOTIFICATIONS: 'marsal_notifications',\n  SETTINGS: 'marsal_settings',\n} as const\n\n// الحصول على جميع الطلبات\nexport function getOrders(): Order[] {\n  if (typeof window === 'undefined') return []\n  \n  const ordersData = localStorage.getItem(STORAGE_KEYS.ORDERS)\n  if (!ordersData) return []\n  \n  try {\n    return JSON.parse(ordersData)\n  } catch {\n    return []\n  }\n}\n\n// إضافة طلب جديد\nexport function addOrder(orderData: Omit<Order, 'id' | 'trackingNumber' | 'statusHistory' | 'createdAt' | 'updatedAt'>): Order {\n  const orders = getOrders()\n  \n  const newOrder: Order = {\n    ...orderData,\n    id: Date.now().toString(),\n    trackingNumber: generateTrackingNumber(),\n    statusHistory: [{\n      id: Date.now().toString(),\n      orderId: '',\n      status: orderData.status,\n      notes: 'تم إنشاء الطلب',\n      updatedBy: 'النظام',\n      updatedAt: new Date().toISOString(),\n    }],\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  }\n  \n  // تحديث معرف الطلب في تاريخ الحالة\n  newOrder.statusHistory[0].orderId = newOrder.id\n  \n  orders.push(newOrder)\n  localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(orders))\n  \n  return newOrder\n}\n\n// تحديث طلب\nexport function updateOrder(orderId: string, updates: Partial<Order>): boolean {\n  const orders = getOrders()\n  const orderIndex = orders.findIndex(o => o.id === orderId)\n  \n  if (orderIndex === -1) return false\n  \n  orders[orderIndex] = {\n    ...orders[orderIndex],\n    ...updates,\n    updatedAt: new Date().toISOString(),\n  }\n  \n  localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(orders))\n  return true\n}\n\n// تحديث حالة الطلب\nexport function updateOrderStatus(\n  orderId: string,\n  status: OrderStatus,\n  notes?: string,\n  image?: string,\n  returnReason?: string,\n  returnedPieces?: number,\n  newAmount?: number,\n  updatedBy: string = 'المستخدم'\n): boolean {\n  const orders = getOrders()\n  const orderIndex = orders.findIndex(o => o.id === orderId)\n  \n  if (orderIndex === -1) return false\n  \n  const order = orders[orderIndex]\n  \n  // إنشاء تحديث جديد للحالة\n  const statusUpdate: OrderStatusUpdate = {\n    id: Date.now().toString(),\n    orderId,\n    status,\n    notes,\n    image,\n    returnReason: returnReason as any,\n    returnedPieces,\n    newAmount,\n    updatedBy,\n    updatedAt: new Date().toISOString(),\n  }\n  \n  // تحديث الطلب\n  order.status = status\n  order.statusHistory.push(statusUpdate)\n  order.updatedAt = new Date().toISOString()\n  \n  // تحديث البيانات الإضافية حسب الحالة\n  if (returnReason) order.returnReason = returnReason as any\n  if (returnedPieces !== undefined) order.returnedPieces = returnedPieces\n  if (newAmount !== undefined) {\n    order.originalAmount = order.originalAmount || order.amount\n    order.amount = newAmount\n  }\n  if (notes) order.notes = notes\n  \n  localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(orders))\n  \n  // إضافة إشعار\n  addNotification({\n    title: 'تحديث حالة الطلب',\n    message: `تم تحديث حالة الطلب ${order.trackingNumber} إلى ${getStatusLabel(status)}`,\n    type: 'info',\n  })\n  \n  return true\n}\n\n// حذف طلب\nexport function deleteOrder(orderId: string): boolean {\n  const orders = getOrders()\n  const filteredOrders = orders.filter(o => o.id !== orderId)\n  \n  if (filteredOrders.length === orders.length) return false\n  \n  localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(filteredOrders))\n  return true\n}\n\n// البحث في الطلبات\nexport function searchOrders(query: string): Order[] {\n  const orders = getOrders()\n  const searchTerm = query.toLowerCase()\n  \n  return orders.filter(order =>\n    order.trackingNumber.toLowerCase().includes(searchTerm) ||\n    order.senderName.toLowerCase().includes(searchTerm) ||\n    order.recipientName.toLowerCase().includes(searchTerm) ||\n    order.recipientPhone.includes(searchTerm) ||\n    order.senderPhone.includes(searchTerm)\n  )\n}\n\n// فلترة الطلبات حسب الحالة\nexport function filterOrdersByStatus(status: OrderStatus): Order[] {\n  return getOrders().filter(order => order.status === status)\n}\n\n// فلترة الطلبات حسب المندوب\nexport function filterOrdersByCourier(courierId: string): Order[] {\n  return getOrders().filter(order => order.courierId === courierId)\n}\n\n// إسناد طلبات لمندوب\nexport function assignOrdersToCourier(orderIds: string[], courierId: string, courierName: string, courierPhone: string): boolean {\n  const orders = getOrders()\n  let updated = false\n  \n  orders.forEach(order => {\n    if (orderIds.includes(order.id)) {\n      order.courierId = courierId\n      order.courierName = courierName\n      order.courierPhone = courierPhone\n      order.status = 'processing'\n      order.updatedAt = new Date().toISOString()\n      \n      // إضافة تحديث للحالة\n      order.statusHistory.push({\n        id: Date.now().toString() + Math.random(),\n        orderId: order.id,\n        status: 'processing',\n        notes: `تم إسناد الطلب للمندوب ${courierName}`,\n        updatedBy: 'النظام',\n        updatedAt: new Date().toISOString(),\n      })\n      \n      updated = true\n    }\n  })\n  \n  if (updated) {\n    localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(orders))\n  }\n  \n  return updated\n}\n\n// تخصيص الطلبات للمندوبين\nexport function assignOrderToCourier(orderId: string, courierId: string, courierName: string): boolean {\n  const orders = getOrders()\n  const orderIndex = orders.findIndex(order => order.id === orderId)\n\n  if (orderIndex === -1) return false\n\n  orders[orderIndex] = {\n    ...orders[orderIndex],\n    courierId,\n    courierName,\n    updatedAt: new Date().toISOString()\n  }\n\n  localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(orders))\n  return true\n}\n\n// دوال إدارة الموظفين\nexport function getEmployees(): User[] {\n  const users = getUsers()\n  return users.filter(user => user.role !== 'manager')\n}\n\nexport function addEmployee(employeeData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): User {\n  const users = getUsers()\n  const newEmployee: User = {\n    ...employeeData,\n    id: generateId(),\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString()\n  }\n\n  users.push(newEmployee)\n  localStorage.setItem('users', JSON.stringify(users))\n  return newEmployee\n}\n\nexport function updateEmployee(employeeId: string, updates: Partial<User>): boolean {\n  const users = getUsers()\n  const index = users.findIndex(user => user.id === employeeId)\n\n  if (index === -1) return false\n\n  users[index] = {\n    ...users[index],\n    ...updates,\n    updatedAt: new Date().toISOString()\n  }\n\n  localStorage.setItem('users', JSON.stringify(users))\n  return true\n}\n\nexport function deleteEmployee(employeeId: string): boolean {\n  const users = getUsers()\n  const filteredUsers = users.filter(user => user.id !== employeeId)\n\n  if (filteredUsers.length === users.length) return false\n\n  localStorage.setItem('users', JSON.stringify(filteredUsers))\n  return true\n}\n\nexport function searchEmployees(query: string): User[] {\n  const employees = getEmployees()\n  const searchTerm = query.toLowerCase()\n\n  return employees.filter(employee =>\n    employee.name.toLowerCase().includes(searchTerm) ||\n    employee.username.toLowerCase().includes(searchTerm) ||\n    (employee.phone && employee.phone.includes(searchTerm)) ||\n    (employee.email && employee.email.toLowerCase().includes(searchTerm))\n  )\n}\n\n// حساب الإحصائيات\nexport function calculateStatistics(courierId?: string): Statistics {\n  const orders = courierId ? filterOrdersByCourier(courierId) : getOrders()\n  \n  const stats: Statistics = {\n    totalOrders: orders.length,\n    pendingOrders: orders.filter(o => o.status === 'pending').length,\n    processingOrders: orders.filter(o => o.status === 'processing').length,\n    shippedOrders: orders.filter(o => o.status === 'shipped').length,\n    outForDeliveryOrders: orders.filter(o => o.status === 'out_for_delivery').length,\n    deliveredOrders: orders.filter(o => o.status === 'delivered').length,\n    returnedOrders: orders.filter(o => o.status === 'returned').length,\n    cancelledOrders: orders.filter(o => o.status === 'cancelled').length,\n    postponedOrders: orders.filter(o => o.status === 'postponed').length,\n    totalRevenue: orders.filter(o => o.status === 'delivered').reduce((sum, o) => sum + o.amount, 0),\n    todayOrders: 0,\n    thisWeekOrders: 0,\n    thisMonthOrders: 0,\n  }\n  \n  // حساب الطلبات حسب التاريخ\n  const today = new Date()\n  const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()))\n  const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)\n  \n  orders.forEach(order => {\n    const orderDate = new Date(order.createdAt)\n    const todayDate = new Date()\n    \n    if (orderDate.toDateString() === todayDate.toDateString()) {\n      stats.todayOrders++\n    }\n    \n    if (orderDate >= startOfWeek) {\n      stats.thisWeekOrders++\n    }\n    \n    if (orderDate >= startOfMonth) {\n      stats.thisMonthOrders++\n    }\n  })\n  \n  return stats\n}\n\n// إدارة الإشعارات\nexport function getNotifications(): Notification[] {\n  if (typeof window === 'undefined') return []\n  \n  const notificationsData = localStorage.getItem(STORAGE_KEYS.NOTIFICATIONS)\n  if (!notificationsData) return []\n  \n  try {\n    return JSON.parse(notificationsData)\n  } catch {\n    return []\n  }\n}\n\nexport function addNotification(notification: Omit<Notification, 'id' | 'isRead' | 'createdAt'>): void {\n  const notifications = getNotifications()\n  \n  const newNotification: Notification = {\n    ...notification,\n    id: Date.now().toString(),\n    isRead: false,\n    createdAt: new Date().toISOString(),\n  }\n  \n  notifications.unshift(newNotification) // إضافة في المقدمة\n  \n  // الاحتفاظ بآخر 100 إشعار فقط\n  if (notifications.length > 100) {\n    notifications.splice(100)\n  }\n  \n  localStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify(notifications))\n}\n\nexport function markNotificationAsRead(notificationId: string): boolean {\n  const notifications = getNotifications()\n  const notification = notifications.find(n => n.id === notificationId)\n  \n  if (!notification) return false\n  \n  notification.isRead = true\n  localStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify(notifications))\n  return true\n}\n\n// دالة مساعدة للحصول على تسمية الحالة\nexport function getStatusLabel(status: OrderStatus): string {\n  const statusLabels: Record<OrderStatus, string> = {\n    pending: 'في الانتظار',\n    processing: 'قيد المعالجة',\n    shipped: 'تم الشحن',\n    out_for_delivery: 'قيد التوصيل',\n    delivered: 'تم التسليم',\n    returned: 'راجع',\n    cancelled: 'ملغي',\n    postponed: 'مؤجل',\n  }\n  \n  return statusLabels[status] || status\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AACA;;AAEA,wBAAwB;AACxB,MAAM,eAAe;IACnB,QAAQ;IACR,eAAe;IACf,UAAU;AACZ;AAGO,SAAS;IACd,uCAAmC;;IAAQ;IAE3C,MAAM,aAAa,aAAa,OAAO,CAAC,aAAa,MAAM;IAC3D,IAAI,CAAC,YAAY,OAAO,EAAE;IAE1B,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAM;QACN,OAAO,EAAE;IACX;AACF;AAGO,SAAS,SAAS,SAA6F;IACpH,MAAM,SAAS;IAEf,MAAM,WAAkB;QACtB,GAAG,SAAS;QACZ,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,gBAAgB,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;QACrC,eAAe;YAAC;gBACd,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,SAAS;gBACT,QAAQ,UAAU,MAAM;gBACxB,OAAO;gBACP,WAAW;gBACX,WAAW,IAAI,OAAO,WAAW;YACnC;SAAE;QACF,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,mCAAmC;IACnC,SAAS,aAAa,CAAC,EAAE,CAAC,OAAO,GAAG,SAAS,EAAE;IAE/C,OAAO,IAAI,CAAC;IACZ,aAAa,OAAO,CAAC,aAAa,MAAM,EAAE,KAAK,SAAS,CAAC;IAEzD,OAAO;AACT;AAGO,SAAS,YAAY,OAAe,EAAE,OAAuB;IAClE,MAAM,SAAS;IACf,MAAM,aAAa,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAElD,IAAI,eAAe,CAAC,GAAG,OAAO;IAE9B,MAAM,CAAC,WAAW,GAAG;QACnB,GAAG,MAAM,CAAC,WAAW;QACrB,GAAG,OAAO;QACV,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,aAAa,OAAO,CAAC,aAAa,MAAM,EAAE,KAAK,SAAS,CAAC;IACzD,OAAO;AACT;AAGO,SAAS,kBACd,OAAe,EACf,MAAmB,EACnB,KAAc,EACd,KAAc,EACd,YAAqB,EACrB,cAAuB,EACvB,SAAkB,EAClB,YAAoB,UAAU;IAE9B,MAAM,SAAS;IACf,MAAM,aAAa,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAElD,IAAI,eAAe,CAAC,GAAG,OAAO;IAE9B,MAAM,QAAQ,MAAM,CAAC,WAAW;IAEhC,0BAA0B;IAC1B,MAAM,eAAkC;QACtC,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB;QACA;QACA;QACA;QACA,cAAc;QACd;QACA;QACA;QACA,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,cAAc;IACd,MAAM,MAAM,GAAG;IACf,MAAM,aAAa,CAAC,IAAI,CAAC;IACzB,MAAM,SAAS,GAAG,IAAI,OAAO,WAAW;IAExC,qCAAqC;IACrC,IAAI,cAAc,MAAM,YAAY,GAAG;IACvC,IAAI,mBAAmB,WAAW,MAAM,cAAc,GAAG;IACzD,IAAI,cAAc,WAAW;QAC3B,MAAM,cAAc,GAAG,MAAM,cAAc,IAAI,MAAM,MAAM;QAC3D,MAAM,MAAM,GAAG;IACjB;IACA,IAAI,OAAO,MAAM,KAAK,GAAG;IAEzB,aAAa,OAAO,CAAC,aAAa,MAAM,EAAE,KAAK,SAAS,CAAC;IAEzD,cAAc;IACd,gBAAgB;QACd,OAAO;QACP,SAAS,CAAC,oBAAoB,EAAE,MAAM,cAAc,CAAC,KAAK,EAAE,eAAe,SAAS;QACpF,MAAM;IACR;IAEA,OAAO;AACT;AAGO,SAAS,YAAY,OAAe;IACzC,MAAM,SAAS;IACf,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAEnD,IAAI,eAAe,MAAM,KAAK,OAAO,MAAM,EAAE,OAAO;IAEpD,aAAa,OAAO,CAAC,aAAa,MAAM,EAAE,KAAK,SAAS,CAAC;IACzD,OAAO;AACT;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,SAAS;IACf,MAAM,aAAa,MAAM,WAAW;IAEpC,OAAO,OAAO,MAAM,CAAC,CAAA,QACnB,MAAM,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC,eAC5C,MAAM,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,eACxC,MAAM,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,eAC3C,MAAM,cAAc,CAAC,QAAQ,CAAC,eAC9B,MAAM,WAAW,CAAC,QAAQ,CAAC;AAE/B;AAGO,SAAS,qBAAqB,MAAmB;IACtD,OAAO,YAAY,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;AACtD;AAGO,SAAS,sBAAsB,SAAiB;IACrD,OAAO,YAAY,MAAM,CAAC,CAAA,QAAS,MAAM,SAAS,KAAK;AACzD;AAGO,SAAS,sBAAsB,QAAkB,EAAE,SAAiB,EAAE,WAAmB,EAAE,YAAoB;IACpH,MAAM,SAAS;IACf,IAAI,UAAU;IAEd,OAAO,OAAO,CAAC,CAAA;QACb,IAAI,SAAS,QAAQ,CAAC,MAAM,EAAE,GAAG;YAC/B,MAAM,SAAS,GAAG;YAClB,MAAM,WAAW,GAAG;YACpB,MAAM,YAAY,GAAG;YACrB,MAAM,MAAM,GAAG;YACf,MAAM,SAAS,GAAG,IAAI,OAAO,WAAW;YAExC,qBAAqB;YACrB,MAAM,aAAa,CAAC,IAAI,CAAC;gBACvB,IAAI,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM;gBACvC,SAAS,MAAM,EAAE;gBACjB,QAAQ;gBACR,OAAO,CAAC,uBAAuB,EAAE,aAAa;gBAC9C,WAAW;gBACX,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,UAAU;QACZ;IACF;IAEA,IAAI,SAAS;QACX,aAAa,OAAO,CAAC,aAAa,MAAM,EAAE,KAAK,SAAS,CAAC;IAC3D;IAEA,OAAO;AACT;AAGO,SAAS,qBAAqB,OAAe,EAAE,SAAiB,EAAE,WAAmB;IAC1F,MAAM,SAAS;IACf,MAAM,aAAa,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAE1D,IAAI,eAAe,CAAC,GAAG,OAAO;IAE9B,MAAM,CAAC,WAAW,GAAG;QACnB,GAAG,MAAM,CAAC,WAAW;QACrB;QACA;QACA,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,aAAa,OAAO,CAAC,aAAa,MAAM,EAAE,KAAK,SAAS,CAAC;IACzD,OAAO;AACT;AAGO,SAAS;IACd,MAAM,QAAQ;IACd,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;AAC5C;AAEO,SAAS,YAAY,YAA0D;IACpF,MAAM,QAAQ;IACd,MAAM,cAAoB;QACxB,GAAG,YAAY;QACf,IAAI;QACJ,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,MAAM,IAAI,CAAC;IACX,aAAa,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC;IAC7C,OAAO;AACT;AAEO,SAAS,eAAe,UAAkB,EAAE,OAAsB;IACvE,MAAM,QAAQ;IACd,MAAM,QAAQ,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAElD,IAAI,UAAU,CAAC,GAAG,OAAO;IAEzB,KAAK,CAAC,MAAM,GAAG;QACb,GAAG,KAAK,CAAC,MAAM;QACf,GAAG,OAAO;QACV,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,aAAa,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC;IAC7C,OAAO;AACT;AAEO,SAAS,eAAe,UAAkB;IAC/C,MAAM,QAAQ;IACd,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAEvD,IAAI,cAAc,MAAM,KAAK,MAAM,MAAM,EAAE,OAAO;IAElD,aAAa,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC;IAC7C,OAAO;AACT;AAEO,SAAS,gBAAgB,KAAa;IAC3C,MAAM,YAAY;IAClB,MAAM,aAAa,MAAM,WAAW;IAEpC,OAAO,UAAU,MAAM,CAAC,CAAA,WACtB,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,eACrC,SAAS,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,eACxC,SAAS,KAAK,IAAI,SAAS,KAAK,CAAC,QAAQ,CAAC,eAC1C,SAAS,KAAK,IAAI,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;AAE7D;AAGO,SAAS,oBAAoB,SAAkB;IACpD,MAAM,SAAS,YAAY,sBAAsB,aAAa;IAE9D,MAAM,QAAoB;QACxB,aAAa,OAAO,MAAM;QAC1B,eAAe,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;QAChE,kBAAkB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,cAAc,MAAM;QACtE,eAAe,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;QAChE,sBAAsB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,oBAAoB,MAAM;QAChF,iBAAiB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;QACpE,gBAAgB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;QAClE,iBAAiB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;QACpE,iBAAiB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;QACpE,cAAc,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QAC9F,aAAa;QACb,gBAAgB;QAChB,iBAAiB;IACnB;IAEA,2BAA2B;IAC3B,MAAM,QAAQ,IAAI;IAClB,MAAM,cAAc,IAAI,KAAK,MAAM,OAAO,CAAC,MAAM,OAAO,KAAK,MAAM,MAAM;IACzE,MAAM,eAAe,IAAI,KAAK,MAAM,WAAW,IAAI,MAAM,QAAQ,IAAI;IAErE,OAAO,OAAO,CAAC,CAAA;QACb,MAAM,YAAY,IAAI,KAAK,MAAM,SAAS;QAC1C,MAAM,YAAY,IAAI;QAEtB,IAAI,UAAU,YAAY,OAAO,UAAU,YAAY,IAAI;YACzD,MAAM,WAAW;QACnB;QAEA,IAAI,aAAa,aAAa;YAC5B,MAAM,cAAc;QACtB;QAEA,IAAI,aAAa,cAAc;YAC7B,MAAM,eAAe;QACvB;IACF;IAEA,OAAO;AACT;AAGO,SAAS;IACd,uCAAmC;;IAAQ;IAE3C,MAAM,oBAAoB,aAAa,OAAO,CAAC,aAAa,aAAa;IACzE,IAAI,CAAC,mBAAmB,OAAO,EAAE;IAEjC,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAM;QACN,OAAO,EAAE;IACX;AACF;AAEO,SAAS,gBAAgB,YAA+D;IAC7F,MAAM,gBAAgB;IAEtB,MAAM,kBAAgC;QACpC,GAAG,YAAY;QACf,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,QAAQ;QACR,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,cAAc,OAAO,CAAC,iBAAiB,mBAAmB;;IAE1D,8BAA8B;IAC9B,IAAI,cAAc,MAAM,GAAG,KAAK;QAC9B,cAAc,MAAM,CAAC;IACvB;IAEA,aAAa,OAAO,CAAC,aAAa,aAAa,EAAE,KAAK,SAAS,CAAC;AAClE;AAEO,SAAS,uBAAuB,cAAsB;IAC3D,MAAM,gBAAgB;IACtB,MAAM,eAAe,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAEtD,IAAI,CAAC,cAAc,OAAO;IAE1B,aAAa,MAAM,GAAG;IACtB,aAAa,OAAO,CAAC,aAAa,aAAa,EAAE,KAAK,SAAS,CAAC;IAChE,OAAO;AACT;AAGO,SAAS,eAAe,MAAmB;IAChD,MAAM,eAA4C;QAChD,SAAS;QACT,YAAY;QACZ,SAAS;QACT,kBAAkB;QAClB,WAAW;QACX,UAAU;QACV,WAAW;QACX,WAAW;IACb;IAEA,OAAO,YAAY,CAAC,OAAO,IAAI;AACjC", "debugId": null}}, {"offset": {"line": 1040, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/src/app/employees/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/components/auth-provider'\nimport { Header } from '@/components/header'\nimport { Navigation } from '@/components/navigation'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { getEmployees, searchEmployees, deleteEmployee } from '@/lib/storage'\nimport { User } from '@/types'\nimport { Plus, Search, Edit, Trash2, Users, Phone, Mail, Calendar } from 'lucide-react'\n\nexport default function EmployeesPage() {\n  const { user, isLoading } = useAuth()\n  const router = useRouter()\n  const [employees, setEmployees] = useState<User[]>([])\n  const [filteredEmployees, setFilteredEmployees] = useState<User[]>([])\n  const [searchQuery, setSearchQuery] = useState('')\n  const [roleFilter, setRoleFilter] = useState<'all' | 'supervisor' | 'courier'>('all')\n  const [isLoadingEmployees, setIsLoadingEmployees] = useState(true)\n\n  useEffect(() => {\n    if (!isLoading && !user) {\n      router.push('/login')\n      return\n    }\n\n    // التحقق من الصلاحيات - فقط المدير والمتابع يمكنهم الوصول\n    if (user && user.role === 'courier') {\n      router.push('/')\n      return\n    }\n\n    if (user) {\n      loadEmployees()\n    }\n  }, [user, isLoading, router])\n\n  const loadEmployees = () => {\n    setIsLoadingEmployees(true)\n    try {\n      let allEmployees = getEmployees()\n      \n      // فلترة الموظفين حسب دور المستخدم\n      if (user?.role === 'supervisor') {\n        // المتابع يرى فقط المندوبين\n        allEmployees = allEmployees.filter(emp => emp.role === 'courier')\n      } else if (user?.role === 'manager') {\n        // المدير يرى المتابعين والمندوبين\n        allEmployees = allEmployees.filter(emp => emp.role !== 'manager')\n      }\n      \n      setEmployees(allEmployees)\n      setFilteredEmployees(allEmployees)\n    } catch (error) {\n      console.error('Error loading employees:', error)\n    } finally {\n      setIsLoadingEmployees(false)\n    }\n  }\n\n  useEffect(() => {\n    let filtered = employees\n\n    // تطبيق البحث\n    if (searchQuery.trim()) {\n      filtered = searchEmployees(searchQuery).filter(emp => {\n        if (user?.role === 'supervisor') {\n          return emp.role === 'courier'\n        } else if (user?.role === 'manager') {\n          return emp.role !== 'manager'\n        }\n        return true\n      })\n    }\n\n    // تطبيق فلتر الدور\n    if (roleFilter !== 'all') {\n      filtered = filtered.filter(emp => emp.role === roleFilter)\n    }\n\n    setFilteredEmployees(filtered)\n  }, [searchQuery, roleFilter, employees, user])\n\n  const handleDeleteEmployee = async (employeeId: string) => {\n    if (window.confirm('هل أنت متأكد من حذف هذا الموظف؟')) {\n      try {\n        const success = deleteEmployee(employeeId)\n        if (success) {\n          loadEmployees()\n        } else {\n          alert('حدث خطأ أثناء حذف الموظف')\n        }\n      } catch (error) {\n        console.error('Error deleting employee:', error)\n        alert('حدث خطأ أثناء حذف الموظف')\n      }\n    }\n  }\n\n  const getRoleLabel = (role: string) => {\n    const labels = {\n      manager: 'مدير',\n      supervisor: 'متابع',\n      courier: 'مندوب'\n    }\n    return labels[role as keyof typeof labels] || role\n  }\n\n  const getRoleColor = (role: string) => {\n    const colors = {\n      manager: 'bg-purple-100 text-purple-800',\n      supervisor: 'bg-blue-100 text-blue-800',\n      courier: 'bg-green-100 text-green-800'\n    }\n    return colors[role as keyof typeof colors] || 'bg-gray-100 text-gray-800'\n  }\n\n  if (isLoading || isLoadingEmployees) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">جاري التحميل...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user || user.role === 'courier') return null\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      <div className=\"flex\">\n        <Navigation />\n        \n        <main className=\"flex-1 p-6\">\n          {/* العنوان والإجراءات */}\n          <div className=\"flex justify-between items-center mb-6\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">إدارة الموظفين</h1>\n              <p className=\"text-gray-600 mt-1\">\n                إجمالي الموظفين: {filteredEmployees.length}\n              </p>\n            </div>\n            \n            <Button\n              onClick={() => router.push('/employees/new')}\n              className=\"flex items-center space-x-2 space-x-reverse\"\n            >\n              <Plus className=\"h-4 w-4\" />\n              <span>موظف جديد</span>\n            </Button>\n          </div>\n\n          {/* شريط البحث والفلاتر */}\n          <Card className=\"mb-6\">\n            <CardContent className=\"p-4\">\n              <div className=\"flex flex-col md:flex-row gap-4\">\n                {/* البحث */}\n                <div className=\"flex-1\">\n                  <div className=\"relative\">\n                    <Search className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n                    <Input\n                      placeholder=\"البحث بالاسم أو اسم المستخدم أو رقم الهاتف...\"\n                      value={searchQuery}\n                      onChange={(e) => setSearchQuery(e.target.value)}\n                      className=\"pr-10\"\n                    />\n                  </div>\n                </div>\n\n                {/* فلتر الدور */}\n                {user.role === 'manager' && (\n                  <div className=\"md:w-48\">\n                    <select\n                      value={roleFilter}\n                      onChange={(e) => setRoleFilter(e.target.value as 'all' | 'supervisor' | 'courier')}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"all\">جميع الأدوار</option>\n                      <option value=\"supervisor\">متابع</option>\n                      <option value=\"courier\">مندوب</option>\n                    </select>\n                  </div>\n                )}\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* قائمة الموظفين */}\n          {filteredEmployees.length === 0 ? (\n            <Card>\n              <CardContent className=\"p-8 text-center\">\n                <div className=\"text-gray-400 mb-4\">\n                  <Users className=\"h-16 w-16 mx-auto\" />\n                </div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">لا يوجد موظفين</h3>\n                <p className=\"text-gray-600\">\n                  {searchQuery || roleFilter !== 'all' \n                    ? 'لم يتم العثور على موظفين يطابقون معايير البحث'\n                    : 'لم يتم إضافة أي موظفين بعد'\n                  }\n                </p>\n              </CardContent>\n            </Card>\n          ) : (\n            <div className=\"grid gap-4\">\n              {filteredEmployees.map((employee) => (\n                <Card key={employee.id} className=\"hover:shadow-md transition-shadow\">\n                  <CardContent className=\"p-6\">\n                    <div className=\"flex justify-between items-start\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center space-x-4 space-x-reverse mb-3\">\n                          <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center\">\n                            <Users className=\"h-6 w-6 text-blue-600\" />\n                          </div>\n                          <div>\n                            <h3 className=\"text-lg font-semibold text-gray-900\">\n                              {employee.name}\n                            </h3>\n                            <p className=\"text-sm text-gray-600\">@{employee.username}</p>\n                          </div>\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRoleColor(employee.role)}`}>\n                            {getRoleLabel(employee.role)}\n                          </span>\n                        </div>\n                        \n                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600\">\n                          <div className=\"flex items-center space-x-2 space-x-reverse\">\n                            <Phone className=\"h-4 w-4\" />\n                            <span>{employee.phone || 'غير محدد'}</span>\n                          </div>\n                          <div className=\"flex items-center space-x-2 space-x-reverse\">\n                            <Mail className=\"h-4 w-4\" />\n                            <span>{employee.email || 'غير محدد'}</span>\n                          </div>\n                          <div className=\"flex items-center space-x-2 space-x-reverse\">\n                            <Calendar className=\"h-4 w-4\" />\n                            <span>\n                              {employee.createdAt \n                                ? new Date(employee.createdAt).toLocaleDateString('ar-IQ')\n                                : 'غير محدد'\n                              }\n                            </span>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"flex space-x-2 space-x-reverse\">\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => router.push(`/employees/${employee.id}/edit`)}\n                        >\n                          <Edit className=\"h-4 w-4\" />\n                        </Button>\n                        \n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => handleDeleteEmployee(employee.id)}\n                          className=\"text-red-600 hover:text-red-700 hover:bg-red-50\"\n                        >\n                          <Trash2 className=\"h-4 w-4\" />\n                        </Button>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          )}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAVA;;;;;;;;;;;AAce,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoC;IAC/E,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,aAAa,CAAC,MAAM;gBACvB,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,0DAA0D;YAC1D,IAAI,QAAQ,KAAK,IAAI,KAAK,WAAW;gBACnC,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,MAAM;gBACR;YACF;QACF;kCAAG;QAAC;QAAM;QAAW;KAAO;IAE5B,MAAM,gBAAgB;QACpB,sBAAsB;QACtB,IAAI;YACF,IAAI,eAAe,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD;YAE9B,kCAAkC;YAClC,IAAI,MAAM,SAAS,cAAc;gBAC/B,4BAA4B;gBAC5B,eAAe,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;YACzD,OAAO,IAAI,MAAM,SAAS,WAAW;gBACnC,kCAAkC;gBAClC,eAAe,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;YACzD;YAEA,aAAa;YACb,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,WAAW;YAEf,cAAc;YACd,IAAI,YAAY,IAAI,IAAI;gBACtB,WAAW,CAAA,GAAA,wHAAA,CAAA,kBAAe,AAAD,EAAE,aAAa,MAAM;+CAAC,CAAA;wBAC7C,IAAI,MAAM,SAAS,cAAc;4BAC/B,OAAO,IAAI,IAAI,KAAK;wBACtB,OAAO,IAAI,MAAM,SAAS,WAAW;4BACnC,OAAO,IAAI,IAAI,KAAK;wBACtB;wBACA,OAAO;oBACT;;YACF;YAEA,mBAAmB;YACnB,IAAI,eAAe,OAAO;gBACxB,WAAW,SAAS,MAAM;+CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;;YACjD;YAEA,qBAAqB;QACvB;kCAAG;QAAC;QAAa;QAAY;QAAW;KAAK;IAE7C,MAAM,uBAAuB,OAAO;QAClC,IAAI,OAAO,OAAO,CAAC,oCAAoC;YACrD,IAAI;gBACF,MAAM,UAAU,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE;gBAC/B,IAAI,SAAS;oBACX;gBACF,OAAO;oBACL,MAAM;gBACR;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,MAAM;YACR;QACF;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAS;YACb,SAAS;YACT,YAAY;YACZ,SAAS;QACX;QACA,OAAO,MAAM,CAAC,KAA4B,IAAI;IAChD;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAS;YACb,SAAS;YACT,YAAY;YACZ,SAAS;QACX;QACA,OAAO,MAAM,CAAC,KAA4B,IAAI;IAChD;IAEA,IAAI,aAAa,oBAAoB;QACnC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,WAAW,OAAO;IAE7C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,SAAM;;;;;0BAEP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,aAAU;;;;;kCAEX,6LAAC;wBAAK,WAAU;;0CAEd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;;oDAAqB;oDACd,kBAAkB,MAAM;;;;;;;;;;;;;kDAI9C,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAKV,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAO,WAAU;;;;;;sEAClB,6LAAC,oIAAA,CAAA,QAAK;4DACJ,aAAY;4DACZ,OAAO;4DACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4DAC9C,WAAU;;;;;;;;;;;;;;;;;4CAMf,KAAK,IAAI,KAAK,2BACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;;sEAEV,6LAAC;4DAAO,OAAM;sEAAM;;;;;;sEACpB,6LAAC;4DAAO,OAAM;sEAAa;;;;;;sEAC3B,6LAAC;4DAAO,OAAM;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BASnC,kBAAkB,MAAM,KAAK,kBAC5B,6LAAC,mIAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAM,WAAU;;;;;;;;;;;sDAEnB,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAE,WAAU;sDACV,eAAe,eAAe,QAC3B,kDACA;;;;;;;;;;;;;;;;qDAMV,6LAAC;gCAAI,WAAU;0CACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,6LAAC,mIAAA,CAAA,OAAI;wCAAmB,WAAU;kDAChC,cAAA,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAM,WAAU;;;;;;;;;;;kFAEnB,6LAAC;;0FACC,6LAAC;gFAAG,WAAU;0FACX,SAAS,IAAI;;;;;;0FAEhB,6LAAC;gFAAE,WAAU;;oFAAwB;oFAAE,SAAS,QAAQ;;;;;;;;;;;;;kFAE1D,6LAAC;wEAAK,WAAW,CAAC,2CAA2C,EAAE,aAAa,SAAS,IAAI,GAAG;kFACzF,aAAa,SAAS,IAAI;;;;;;;;;;;;0EAI/B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAM,WAAU;;;;;;0FACjB,6LAAC;0FAAM,SAAS,KAAK,IAAI;;;;;;;;;;;;kFAE3B,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;;;;;;0FAChB,6LAAC;0FAAM,SAAS,KAAK,IAAI;;;;;;;;;;;;kFAE3B,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAS,WAAU;;;;;;0FACpB,6LAAC;0FACE,SAAS,SAAS,GACf,IAAI,KAAK,SAAS,SAAS,EAAE,kBAAkB,CAAC,WAChD;;;;;;;;;;;;;;;;;;;;;;;;kEAOZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,SAAS,EAAE,CAAC,KAAK,CAAC;0EAE3D,cAAA,6LAAC;oEAAK,WAAU;;;;;;;;;;;0EAGlB,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,qBAAqB,SAAS,EAAE;gEAC/C,WAAU;0EAEV,cAAA,6LAAC;oEAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAvDjB,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoEtC;GA3QwB;;QACM,yIAAA,CAAA,UAAO;QACpB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 1689, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1695, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/node_modules/next/src/shared/lib/router/utils/querystring.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nexport function searchParamsToUrlQuery(\n  searchParams: URLSearchParams\n): ParsedUrlQuery {\n  const query: ParsedUrlQuery = {}\n  for (const [key, value] of searchParams.entries()) {\n    const existing = query[key]\n    if (typeof existing === 'undefined') {\n      query[key] = value\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      query[key] = [existing, value]\n    }\n  }\n  return query\n}\n\nfunction stringifyUrlQueryParam(param: unknown): string {\n  if (typeof param === 'string') {\n    return param\n  }\n\n  if (\n    (typeof param === 'number' && !isNaN(param)) ||\n    typeof param === 'boolean'\n  ) {\n    return String(param)\n  } else {\n    return ''\n  }\n}\n\nexport function urlQueryToSearchParams(query: ParsedUrlQuery): URLSearchParams {\n  const searchParams = new URLSearchParams()\n  for (const [key, value] of Object.entries(query)) {\n    if (Array.isArray(value)) {\n      for (const item of value) {\n        searchParams.append(key, stringifyUrlQueryParam(item))\n      }\n    } else {\n      searchParams.set(key, stringifyUrlQueryParam(value))\n    }\n  }\n  return searchParams\n}\n\nexport function assign(\n  target: URLSearchParams,\n  ...searchParamsList: URLSearchParams[]\n): URLSearchParams {\n  for (const searchParams of searchParamsList) {\n    for (const key of searchParams.keys()) {\n      target.delete(key)\n    }\n\n    for (const [key, value] of searchParams.entries()) {\n      target.append(key, value)\n    }\n  }\n\n  return target\n}\n"], "names": ["assign", "searchParamsToUrlQuery", "urlQueryToSearchParams", "searchParams", "query", "key", "value", "entries", "existing", "Array", "isArray", "push", "stringifyUrlQueryParam", "param", "isNaN", "String", "URLSearchParams", "Object", "item", "append", "set", "target", "searchParamsList", "keys", "delete"], "mappings": ";;;;;;;;;;;;;;;;IAgDgBA,MAAM,EAAA;eAANA;;IA9CAC,sBAAsB,EAAA;eAAtBA;;IAgCAC,sBAAsB,EAAA;eAAtBA;;;AAhCT,SAASD,uBACdE,YAA6B;IAE7B,MAAMC,QAAwB,CAAC;IAC/B,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;QACjD,MAAMC,WAAWJ,KAAK,CAACC,IAAI;QAC3B,IAAI,OAAOG,aAAa,aAAa;YACnCJ,KAAK,CAACC,IAAI,GAAGC;QACf,OAAO,IAAIG,MAAMC,OAAO,CAACF,WAAW;YAClCA,SAASG,IAAI,CAACL;QAChB,OAAO;YACLF,KAAK,CAACC,IAAI,GAAG;gBAACG;gBAAUF;aAAM;QAChC;IACF;IACA,OAAOF;AACT;AAEA,SAASQ,uBAAuBC,KAAc;IAC5C,IAAI,OAAOA,UAAU,UAAU;QAC7B,OAAOA;IACT;IAEA,IACG,OAAOA,UAAU,YAAY,CAACC,MAAMD,UACrC,OAAOA,UAAU,WACjB;QACA,OAAOE,OAAOF;IAChB,OAAO;QACL,OAAO;IACT;AACF;AAEO,SAASX,uBAAuBE,KAAqB;IAC1D,MAAMD,eAAe,IAAIa;IACzB,KAAK,MAAM,CAACX,KAAKC,MAAM,IAAIW,OAAOV,OAAO,CAACH,OAAQ;QAChD,IAAIK,MAAMC,OAAO,CAACJ,QAAQ;YACxB,KAAK,MAAMY,QAAQZ,MAAO;gBACxBH,aAAagB,MAAM,CAACd,KAAKO,uBAAuBM;YAClD;QACF,OAAO;YACLf,aAAaiB,GAAG,CAACf,KAAKO,uBAAuBN;QAC/C;IACF;IACA,OAAOH;AACT;AAEO,SAASH,OACdqB,MAAuB;IACvB,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGC,mBAAH,IAAA,MAAA,OAAA,IAAA,OAAA,IAAA,IAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,gBAAAA,CAAH,OAAA,EAAA,GAAA,SAAA,CAAA,KAAsC;;IAEtC,KAAK,MAAMnB,gBAAgBmB,iBAAkB;QAC3C,KAAK,MAAMjB,OAAOF,aAAaoB,IAAI,GAAI;YACrCF,OAAOG,MAAM,CAACnB;QAChB;QAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;YACjDc,OAAOF,MAAM,CAACd,KAAKC;QACrB;IACF;IAEA,OAAOe;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1780, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/node_modules/next/src/shared/lib/router/utils/format-url.ts"], "sourcesContent": ["// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nimport type { UrlObject } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport * as querystring from './querystring'\n\nconst slashedProtocols = /https?|ftp|gopher|file/\n\nexport function formatUrl(urlObj: UrlObject) {\n  let { auth, hostname } = urlObj\n  let protocol = urlObj.protocol || ''\n  let pathname = urlObj.pathname || ''\n  let hash = urlObj.hash || ''\n  let query = urlObj.query || ''\n  let host: string | false = false\n\n  auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : ''\n\n  if (urlObj.host) {\n    host = auth + urlObj.host\n  } else if (hostname) {\n    host = auth + (~hostname.indexOf(':') ? `[${hostname}]` : hostname)\n    if (urlObj.port) {\n      host += ':' + urlObj.port\n    }\n  }\n\n  if (query && typeof query === 'object') {\n    query = String(querystring.urlQueryToSearchParams(query as ParsedUrlQuery))\n  }\n\n  let search = urlObj.search || (query && `?${query}`) || ''\n\n  if (protocol && !protocol.endsWith(':')) protocol += ':'\n\n  if (\n    urlObj.slashes ||\n    ((!protocol || slashedProtocols.test(protocol)) && host !== false)\n  ) {\n    host = '//' + (host || '')\n    if (pathname && pathname[0] !== '/') pathname = '/' + pathname\n  } else if (!host) {\n    host = ''\n  }\n\n  if (hash && hash[0] !== '#') hash = '#' + hash\n  if (search && search[0] !== '?') search = '?' + search\n\n  pathname = pathname.replace(/[?#]/g, encodeURIComponent)\n  search = search.replace('#', '%23')\n\n  return `${protocol}${host}${pathname}${search}${hash}`\n}\n\nexport const urlObjectKeys = [\n  'auth',\n  'hash',\n  'host',\n  'hostname',\n  'href',\n  'path',\n  'pathname',\n  'port',\n  'protocol',\n  'query',\n  'search',\n  'slashes',\n]\n\nexport function formatWithValidation(url: UrlObject): string {\n  if (process.env.NODE_ENV === 'development') {\n    if (url !== null && typeof url === 'object') {\n      Object.keys(url).forEach((key) => {\n        if (!urlObjectKeys.includes(key)) {\n          console.warn(\n            `Unknown key passed via urlObject into url.format: ${key}`\n          )\n        }\n      })\n    }\n  }\n\n  return formatUrl(url)\n}\n"], "names": ["formatUrl", "formatWithValidation", "urlObjectKeys", "slashedProtocols", "url<PERSON>bj", "auth", "hostname", "protocol", "pathname", "hash", "query", "host", "encodeURIComponent", "replace", "indexOf", "port", "String", "querystring", "urlQueryToSearchParams", "search", "endsWith", "slashes", "test", "url", "process", "env", "NODE_ENV", "Object", "keys", "for<PERSON>ach", "key", "includes", "console", "warn"], "mappings": "AAAA,uCAAuC;AACvC,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAsEnCwB,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;IA9Df1B,SAAS,EAAA;eAATA;;IA6DAC,oBAAoB,EAAA;eAApBA;;IAfHC,aAAa,EAAA;eAAbA;;;;uEAlDgB;AAE7B,MAAMC,mBAAmB;AAElB,SAASH,UAAUI,MAAiB;IACzC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAE,GAAGF;IACzB,IAAIG,WAAWH,OAAOG,QAAQ,IAAI;IAClC,IAAIC,WAAWJ,OAAOI,QAAQ,IAAI;IAClC,IAAIC,OAAOL,OAAOK,IAAI,IAAI;IAC1B,IAAIC,QAAQN,OAAOM,KAAK,IAAI;IAC5B,IAAIC,OAAuB;IAE3BN,OAAOA,OAAOO,mBAAmBP,MAAMQ,OAAO,CAAC,QAAQ,OAAO,MAAM;IAEpE,IAAIT,OAAOO,IAAI,EAAE;QACfA,OAAON,OAAOD,OAAOO,IAAI;IAC3B,OAAO,IAAIL,UAAU;QACnBK,OAAON,OAAQ,CAAA,CAACC,SAASQ,OAAO,CAAC,OAAQ,MAAGR,WAAS,MAAKA,QAAO;QACjE,IAAIF,OAAOW,IAAI,EAAE;YACfJ,QAAQ,MAAMP,OAAOW,IAAI;QAC3B;IACF;IAEA,IAAIL,SAAS,OAAOA,UAAU,UAAU;QACtCA,QAAQM,OAAOC,aAAYC,sBAAsB,CAACR;IACpD;IAEA,IAAIS,SAASf,OAAOe,MAAM,IAAKT,SAAU,MAAGA,SAAY;IAExD,IAAIH,YAAY,CAACA,SAASa,QAAQ,CAAC,MAAMb,YAAY;IAErD,IACEH,OAAOiB,OAAO,IACZ,CAAA,CAACd,YAAYJ,iBAAiBmB,IAAI,CAACf,SAAQ,KAAMI,SAAS,OAC5D;QACAA,OAAO,OAAQA,CAAAA,QAAQ,EAAC;QACxB,IAAIH,YAAYA,QAAQ,CAAC,EAAE,KAAK,KAAKA,WAAW,MAAMA;IACxD,OAAO,IAAI,CAACG,MAAM;QAChBA,OAAO;IACT;IAEA,IAAIF,QAAQA,IAAI,CAAC,EAAE,KAAK,KAAKA,OAAO,MAAMA;IAC1C,IAAIU,UAAUA,MAAM,CAAC,EAAE,KAAK,KAAKA,SAAS,MAAMA;IAEhDX,WAAWA,SAASK,OAAO,CAAC,SAASD;IACrCO,SAASA,OAAON,OAAO,CAAC,KAAK;IAE7B,OAAQ,KAAEN,WAAWI,OAAOH,WAAWW,SAASV;AAClD;AAEO,MAAMP,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAASD,qBAAqBsB,GAAc;IACjD,wCAA4C;QAC1C,IAAIA,QAAQ,QAAQ,OAAOA,QAAQ,UAAU;YAC3CI,OAAOC,IAAI,CAACL,KAAKM,OAAO,CAAC,CAACC;gBACxB,IAAI,CAAC5B,cAAc6B,QAAQ,CAACD,MAAM;oBAChCE,QAAQC,IAAI,CACT,uDAAoDH;gBAEzD;YACF;QACF;IACF;IAEA,OAAO9B,UAAUuB;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1895, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/node_modules/next/src/client/use-merged-ref.ts"], "sourcesContent": ["import { useCallback, useRef, type Ref } from 'react'\n\n// This is a compatibility hook to support React 18 and 19 refs.\n// In 19, a cleanup function from refs may be returned.\n// In 18, returning a cleanup function creates a warning.\n// Since we take userspace refs, we don't know ahead of time if a cleanup function will be returned.\n// This implements cleanup functions with the old behavior in 18.\n// We know refs are always called alternating with `null` and then `T`.\n// So a call with `null` means we need to call the previous cleanup functions.\nexport function useMergedRef<TElement>(\n  refA: Ref<TElement>,\n  refB: Ref<TElement>\n): Ref<TElement> {\n  const cleanupA = useRef<(() => void) | null>(null)\n  const cleanupB = useRef<(() => void) | null>(null)\n\n  // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n  // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n  // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n  // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n  // (because it hasn't been updated for React 19)\n  // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n  // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n  return useCallback(\n    (current: TElement | null): void => {\n      if (current === null) {\n        const cleanupFnA = cleanupA.current\n        if (cleanupFnA) {\n          cleanupA.current = null\n          cleanupFnA()\n        }\n        const cleanupFnB = cleanupB.current\n        if (cleanupFnB) {\n          cleanupB.current = null\n          cleanupFnB()\n        }\n      } else {\n        if (refA) {\n          cleanupA.current = applyRef(refA, current)\n        }\n        if (refB) {\n          cleanupB.current = applyRef(refB, current)\n        }\n      }\n    },\n    [refA, refB]\n  )\n}\n\nfunction applyRef<TElement>(\n  refA: NonNullable<Ref<TElement>>,\n  current: TElement\n) {\n  if (typeof refA === 'function') {\n    const cleanup = refA(current)\n    if (typeof cleanup === 'function') {\n      return cleanup\n    } else {\n      return () => refA(null)\n    }\n  } else {\n    refA.current = current\n    return () => {\n      refA.current = null\n    }\n  }\n}\n"], "names": ["useMergedRef", "refA", "refB", "cleanupA", "useRef", "cleanupB", "useCallback", "current", "cleanupFnA", "cleanupFnB", "applyRef", "cleanup"], "mappings": ";;;;+BASgBA,gBAAAA;;;eAAAA;;;uBAT8B;AASvC,SAASA,aACdC,IAAmB,EACnBC,IAAmB;IAEnB,MAAMC,WAAWC,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAC7C,MAAMC,WAAWD,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAE7C,mFAAmF;IACnF,yEAAyE;IACzE,iGAAiG;IACjG,8FAA8F;IAC9F,gDAAgD;IAChD,mGAAmG;IACnG,wFAAwF;IACxF,OAAOE,CAAAA,GAAAA,OAAAA,WAAW,EAChB,CAACC;QACC,IAAIA,YAAY,MAAM;YACpB,MAAMC,aAAaL,SAASI,OAAO;YACnC,IAAIC,YAAY;gBACdL,SAASI,OAAO,GAAG;gBACnBC;YACF;YACA,MAAMC,aAAaJ,SAASE,OAAO;YACnC,IAAIE,YAAY;gBACdJ,SAASE,OAAO,GAAG;gBACnBE;YACF;QACF,OAAO;YACL,IAAIR,MAAM;gBACRE,SAASI,OAAO,GAAGG,SAAST,MAAMM;YACpC;YACA,IAAIL,MAAM;gBACRG,SAASE,OAAO,GAAGG,SAASR,MAAMK;YACpC;QACF;IACF,GACA;QAACN;QAAMC;KAAK;AAEhB;AAEA,SAASQ,SACPT,IAAgC,EAChCM,OAAiB;IAEjB,IAAI,OAAON,SAAS,YAAY;QAC9B,MAAMU,UAAUV,KAAKM;QACrB,IAAI,OAAOI,YAAY,YAAY;YACjC,OAAOA;QACT,OAAO;YACL,OAAO,IAAMV,KAAK;QACpB;IACF,OAAO;QACLA,KAAKM,OAAO,GAAGA;QACf,OAAO;YACLN,KAAKM,OAAO,GAAG;QACjB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1968, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/node_modules/next/src/shared/lib/utils.ts"], "sourcesContent": ["import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n"], "names": ["DecodeError", "MiddlewareNotFoundError", "MissingStaticPage", "NormalizeError", "PageNotFoundError", "SP", "ST", "WEB_VITALS", "execOnce", "getDisplayName", "getLocationOrigin", "getURL", "isAbsoluteUrl", "isResSent", "loadGetInitialProps", "normalizeRepeatedSlashes", "stringifyError", "fn", "used", "result", "args", "ABSOLUTE_URL_REGEX", "url", "test", "protocol", "hostname", "port", "window", "location", "href", "origin", "substring", "length", "Component", "displayName", "name", "res", "finished", "headersSent", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replace", "slice", "join", "App", "ctx", "process", "env", "NODE_ENV", "prototype", "getInitialProps", "message", "Error", "pageProps", "props", "Object", "keys", "console", "warn", "performance", "every", "method", "constructor", "page", "code", "error", "JSON", "stringify", "stack"], "mappings": "AA8WM+C,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsDlBjD,WAAW,EAAA;eAAXA;;IAoBAC,uBAAuB,EAAA;eAAvBA;;IAPAC,iBAAiB,EAAA;eAAjBA;;IAZAC,cAAc,EAAA;eAAdA;;IACAC,iBAAiB,EAAA;eAAjBA;;IATAC,EAAE,EAAA;eAAFA;;IACAC,EAAE,EAAA;eAAFA;;IAlXAC,UAAU,EAAA;eAAVA;;IAsQGC,QAAQ,EAAA;eAARA;;IA+BAC,cAAc,EAAA;eAAdA;;IAXAC,iBAAiB,EAAA;eAAjBA;;IAKAC,MAAM,EAAA;eAANA;;IAPHC,aAAa,EAAA;eAAbA;;IAmBGC,SAAS,EAAA;eAATA;;IAkBMC,mBAAmB,EAAA;eAAnBA;;IAdNC,wBAAwB,EAAA;eAAxBA;;IA+GAC,cAAc,EAAA;eAAdA;;;AA9ZT,MAAMT,aAAa;IAAC;IAAO;IAAO;IAAO;IAAO;IAAO;CAAO;AAsQ9D,SAASC,SACdS,EAAK;IAEL,IAAIC,OAAO;IACX,IAAIC;IAEJ,OAAQ;yCAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QACV,IAAI,CAACF,MAAM;YACTA,OAAO;YACPC,SAASF,MAAMG;QACjB;QACA,OAAOD;IACT;AACF;AAEA,0DAA0D;AAC1D,gEAAgE;AAChE,MAAME,qBAAqB;AACpB,MAAMT,gBAAgB,CAACU,MAAgBD,mBAAmBE,IAAI,CAACD;AAE/D,SAASZ;IACd,MAAM,EAAEc,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGC,OAAOC,QAAQ;IACpD,OAAUJ,WAAS,OAAIC,WAAWC,CAAAA,OAAO,MAAMA,OAAO,EAAC;AACzD;AAEO,SAASf;IACd,MAAM,EAAEkB,IAAI,EAAE,GAAGF,OAAOC,QAAQ;IAChC,MAAME,SAASpB;IACf,OAAOmB,KAAKE,SAAS,CAACD,OAAOE,MAAM;AACrC;AAEO,SAASvB,eAAkBwB,SAA2B;IAC3D,OAAO,OAAOA,cAAc,WACxBA,YACAA,UAAUC,WAAW,IAAID,UAAUE,IAAI,IAAI;AACjD;AAEO,SAAStB,UAAUuB,GAAmB;IAC3C,OAAOA,IAAIC,QAAQ,IAAID,IAAIE,WAAW;AACxC;AAEO,SAASvB,yBAAyBO,GAAW;IAClD,MAAMiB,WAAWjB,IAAIkB,KAAK,CAAC;IAC3B,MAAMC,aAAaF,QAAQ,CAAC,EAAE;IAE9B,OACEE,WACE,4DAA4D;IAC5D,0CAA0C;KACzCC,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,UAAU,OACpBH,CAAAA,QAAQ,CAAC,EAAE,GAAI,MAAGA,SAASI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS,EAAC;AAExD;AAEO,eAAe9B,oBAIpB+B,GAAgC,EAAEC,GAAM;IACxC,wCAA2C;YACrCD;QAAJ,IAAA,CAAIA,iBAAAA,IAAIK,SAAS,KAAA,OAAA,KAAA,IAAbL,eAAeM,eAAe,EAAE;YAClC,MAAMC,UAAW,MAAG3C,eAClBoC,OACA;YACF,MAAM,OAAA,cAAkB,CAAlB,IAAIQ,MAAMD,UAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAiB;QACzB;IACF;IACA,iDAAiD;IACjD,MAAMhB,MAAMU,IAAIV,GAAG,IAAKU,IAAIA,GAAG,IAAIA,IAAIA,GAAG,CAACV,GAAG;IAE9C,IAAI,CAACS,IAAIM,eAAe,EAAE;QACxB,IAAIL,IAAIA,GAAG,IAAIA,IAAIb,SAAS,EAAE;YAC5B,+BAA+B;YAC/B,OAAO;gBACLqB,WAAW,MAAMxC,oBAAoBgC,IAAIb,SAAS,EAAEa,IAAIA,GAAG;YAC7D;QACF;QACA,OAAO,CAAC;IACV;IAEA,MAAMS,QAAQ,MAAMV,IAAIM,eAAe,CAACL;IAExC,IAAIV,OAAOvB,UAAUuB,MAAM;QACzB,OAAOmB;IACT;IAEA,IAAI,CAACA,OAAO;QACV,MAAMH,UAAW,MAAG3C,eAClBoC,OACA,iEAA8DU,QAAM;QACtE,MAAM,OAAA,cAAkB,CAAlB,IAAIF,MAAMD,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;IAEA,IAAIL,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAIO,OAAOC,IAAI,CAACF,OAAOvB,MAAM,KAAK,KAAK,CAACc,IAAIA,GAAG,EAAE;YAC/CY,QAAQC,IAAI,CACT,KAAElD,eACDoC,OACA;QAEN;IACF;IAEA,OAAOU;AACT;AAEO,MAAMlD,KAAK,OAAOuD,gBAAgB;AAClC,MAAMtD,KACXD,MACC;IAAC;IAAQ;IAAW;CAAmB,CAAWwD,KAAK,CACtD,CAACC,SAAW,OAAOF,WAAW,CAACE,OAAO,KAAK;AAGxC,MAAM9D,oBAAoBqD;AAAO;AACjC,MAAMlD,uBAAuBkD;AAAO;AACpC,MAAMjD,0BAA0BiD;IAGrCU,YAAYC,IAAY,CAAE;QACxB,KAAK;QACL,IAAI,CAACC,IAAI,GAAG;QACZ,IAAI,CAAC9B,IAAI,GAAG;QACZ,IAAI,CAACiB,OAAO,GAAI,kCAA+BY;IACjD;AACF;AAEO,MAAM9D,0BAA0BmD;IACrCU,YAAYC,IAAY,EAAEZ,OAAe,CAAE;QACzC,KAAK;QACL,IAAI,CAACA,OAAO,GAAI,0CAAuCY,OAAK,MAAGZ;IACjE;AACF;AAEO,MAAMnD,gCAAgCoD;IAE3CU,aAAc;QACZ,KAAK;QACL,IAAI,CAACE,IAAI,GAAG;QACZ,IAAI,CAACb,OAAO,GAAI;IAClB;AACF;AAWO,SAASpC,eAAekD,KAAY;IACzC,OAAOC,KAAKC,SAAS,CAAC;QAAEhB,SAASc,MAAMd,OAAO;QAAEiB,OAAOH,MAAMG,KAAK;IAAC;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2183, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/node_modules/next/src/shared/lib/router/utils/is-local-url.ts"], "sourcesContent": ["import { isAbsoluteUrl, getLocationOrigin } from '../../utils'\nimport { hasBasePath } from '../../../../client/has-base-path'\n\n/**\n * Detects whether a given url is routable by the Next.js router (browser only).\n */\nexport function isLocalURL(url: string): boolean {\n  // prevent a hydration mismatch on href for url with anchor refs\n  if (!isAbsoluteUrl(url)) return true\n  try {\n    // absolute urls can be local if they are on the same origin\n    const locationOrigin = getLocationOrigin()\n    const resolved = new URL(url, locationOrigin)\n    return resolved.origin === locationOrigin && hasBasePath(resolved.pathname)\n  } catch (_) {\n    return false\n  }\n}\n"], "names": ["isLocalURL", "url", "isAbsoluteUrl", "locationOrigin", "getLocationOrigin", "resolved", "URL", "origin", "has<PERSON>ase<PERSON><PERSON>", "pathname", "_"], "mappings": ";;;;+BAMgBA,cAAAA;;;eAAAA;;;uBANiC;6BACrB;AAKrB,SAASA,WAAWC,GAAW;IACpC,gEAAgE;IAChE,IAAI,CAACC,CAAAA,GAAAA,OAAAA,aAAa,EAACD,MAAM,OAAO;IAChC,IAAI;QACF,4DAA4D;QAC5D,MAAME,iBAAiBC,CAAAA,GAAAA,OAAAA,iBAAiB;QACxC,MAAMC,WAAW,IAAIC,IAAIL,KAAKE;QAC9B,OAAOE,SAASE,MAAM,KAAKJ,kBAAkBK,CAAAA,GAAAA,aAAAA,WAAW,EAACH,SAASI,QAAQ;IAC5E,EAAE,OAAOC,GAAG;QACV,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2212, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/node_modules/next/src/shared/lib/utils/error-once.ts"], "sourcesContent": ["let errorOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const errors = new Set<string>()\n  errorOnce = (msg: string) => {\n    if (!errors.has(msg)) {\n      console.error(msg)\n    }\n    errors.add(msg)\n  }\n}\n\nexport { errorOnce }\n"], "names": ["errorOnce", "_", "process", "env", "NODE_ENV", "errors", "Set", "msg", "has", "console", "error", "add"], "mappings": "AACIE,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAUpBJ,aAAAA;;;eAAAA;;;AAXT,IAAIA,YAAY,CAACC,KAAe;AAChC,wCAA2C;IACzC,MAAMI,SAAS,IAAIC;IACnBN,YAAY,CAACO;QACX,IAAI,CAACF,OAAOG,GAAG,CAACD,MAAM;YACpBE,QAAQC,KAAK,CAACH;QAChB;QACAF,OAAOM,GAAG,CAACJ;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2238, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/node_modules/next/src/client/app-dir/link.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport { dispatchNavigateAction } from '../components/app-router-instance'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `null` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | null\n\n  /**\n   * (unstable) Switch to a dynamic prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    if (onNavigate) {\n      let isDefaultPrevented = false\n\n      onNavigate({\n        preventDefault: () => {\n          isDefaultPrevented = true\n        },\n      })\n\n      if (isDefaultPrevented) {\n        return\n      }\n    }\n\n    dispatchNavigateAction(\n      as || href,\n      replace ? 'replace' : 'push',\n      scroll ?? true,\n      linkInstanceRef.current\n    )\n  }\n\n  React.startTransition(navigate)\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n  /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */\n  const appPrefetchKind =\n    prefetchProp === null ? PrefetchKind.AUTO : PrefetchKind.FULL\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'prefetch' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          appPrefetchKind,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, appPrefetchKind, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (e.defaultPrevented) {\n        return\n      }\n\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user.\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n"], "names": ["LinkComponent", "useLinkStatus", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "href", "as", "linkInstanceRef", "replace", "scroll", "onNavigate", "nodeName", "isAnchorNodeName", "toUpperCase", "hasAttribute", "isLocalURL", "preventDefault", "location", "navigate", "isDefaultPrevented", "dispatchNavigateAction", "current", "React", "startTransition", "formatStringOrUrl", "urlObjOrString", "formatUrl", "props", "linkStatus", "setOptimisticLinkStatus", "useOptimistic", "IDLE_LINK_STATUS", "children", "useRef", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "shallow", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "ref", "forwardedRef", "unstable_dynamicOnHover", "restProps", "a", "router", "useContext", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "process", "env", "NODE_ENV", "createPropError", "args", "Error", "key", "expected", "actual", "window", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "locale", "warnOnce", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "child", "console", "warn", "Children", "only", "err", "type", "childRef", "observeLinkVisibilityOnMount", "useCallback", "element", "mountLinkInstance", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "mergedRef", "useMergedRef", "childProps", "defaultPrevented", "upgradeToDynamicPrefetch", "onNavigationIntent", "__NEXT_LINK_NO_TOUCH_START", "undefined", "isAbsoluteUrl", "addBasePath", "link", "errorOnce", "cloneElement", "LinkStatusContext", "Provider", "value", "createContext"], "mappings": "AAkXMuE,QAAQC,GAAG,CAACC,QAAQ,KAAK;AAlX/B;;;;;;;;;;;;;;;;IAmTA;;;;;;;;;CASC,GACD,OAyZC,EAAA;eAzZuBzE;;IA+ZXC,aAAa,EAAA;eAAbA;;;;;iEA1tB2D;2BAE9C;+CACO;oCACJ;8BACA;uBACC;6BACF;0BACH;uBASlB;4BACoB;mCACY;2BACb;AA0M1B,SAASC,gBAAgBC,KAAuB;IAC9C,MAAMC,cAAcD,MAAME,aAAa;IACvC,MAAMC,SAASF,YAAYG,YAAY,CAAC;IACxC,OACGD,UAAUA,WAAW,WACtBH,MAAMK,OAAO,IACbL,MAAMM,OAAO,IACbN,MAAMO,QAAQ,IACdP,MAAMQ,MAAM,IAAI,6BAA6B;IAC5CR,MAAMS,WAAW,IAAIT,MAAMS,WAAW,CAACC,KAAK,KAAK;AAEtD;AAEA,SAASC,YACPC,CAAmB,EACnBC,IAAY,EACZC,EAAU,EACVC,eAAqD,EACrDC,OAAiB,EACjBC,MAAgB,EAChBC,UAAmC;IAEnC,MAAM,EAAEC,QAAQ,EAAE,GAAGP,EAAEV,aAAa;IAEpC,kDAAkD;IAClD,MAAMkB,mBAAmBD,SAASE,WAAW,OAAO;IAEpD,IACGD,oBAAoBrB,gBAAgBa,MACrCA,EAAEV,aAAa,CAACoB,YAAY,CAAC,aAC7B;QACA,8CAA8C;QAC9C;IACF;IAEA,IAAI,CAACC,CAAAA,GAAAA,YAAAA,UAAU,EAACV,OAAO;QACrB,IAAIG,SAAS;YACX,8DAA8D;YAC9D,+BAA+B;YAC/BJ,EAAEY,cAAc;YAChBC,SAAST,OAAO,CAACH;QACnB;QAEA,8CAA8C;QAC9C;IACF;IAEAD,EAAEY,cAAc;IAEhB,MAAME,WAAW;QACf,IAAIR,YAAY;YACd,IAAIS,qBAAqB;YAEzBT,WAAW;gBACTM,gBAAgB;oBACdG,qBAAqB;gBACvB;YACF;YAEA,IAAIA,oBAAoB;gBACtB;YACF;QACF;QAEAC,CAAAA,GAAAA,mBAAAA,sBAAsB,EACpBd,MAAMD,MACNG,UAAU,YAAY,QACtBC,UAAAA,OAAAA,SAAU,MACVF,gBAAgBc,OAAO;IAE3B;IAEAC,OAAAA,OAAK,CAACC,eAAe,CAACL;AACxB;AAEA,SAASM,kBAAkBC,cAAkC;IAC3D,IAAI,OAAOA,mBAAmB,UAAU;QACtC,OAAOA;IACT;IAEA,OAAOC,CAAAA,GAAAA,WAAAA,SAAS,EAACD;AACnB;AAYe,SAASpC,cACtBsC,KAGC;IAED,MAAM,CAACC,YAAYC,wBAAwB,GAAGC,CAAAA,GAAAA,OAAAA,aAAa,EAACC,OAAAA,gBAAgB;IAE5E,IAAIC;IAEJ,MAAMzB,kBAAkB0B,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAEpD,MAAM,EACJ5B,MAAM6B,QAAQ,EACd5B,IAAI6B,MAAM,EACVH,UAAUI,YAAY,EACtBC,UAAUC,eAAe,IAAI,EAC7BC,QAAQ,EACR/B,OAAO,EACPgC,OAAO,EACP/B,MAAM,EACNgC,OAAO,EACPC,cAAcC,gBAAgB,EAC9BC,cAAcC,gBAAgB,EAC9BC,iBAAiB,KAAK,EACtBpC,UAAU,EACVqC,KAAKC,YAAY,EACjBC,uBAAuB,EACvB,GAAGC,WACJ,GAAGvB;IAEJK,WAAWI;IAEX,IACEU,kBACC,CAAA,OAAOd,aAAa,YAAY,OAAOA,aAAa,QAAO,GAC5D;QACAA,WAAAA,WAAAA,GAAW,CAAA,GAAA,YAAA,GAAA,EAACmB,KAAAA;sBAAGnB;;IACjB;IAEA,MAAMoB,SAAS9B,OAAAA,OAAK,CAAC+B,UAAU,CAACC,+BAAAA,gBAAgB;IAEhD,MAAMC,kBAAkBjB,iBAAiB;IACzC;;;;;;GAMC,GACD,MAAMkB,kBACJlB,iBAAiB,OAAOmB,oBAAAA,YAAY,CAACC,IAAI,GAAGD,oBAAAA,YAAY,CAACE,IAAI;IAE/D,wCAA2C;QACzC,SAASI,gBAAgBC,IAIxB;YACC,OAAO,OAAA,cAKN,CALM,IAAIC,MACR,iCAA+BD,KAAKE,GAAG,GAAC,iBAAeF,KAAKG,QAAQ,GAAC,4BAA4BH,KAAKI,MAAM,GAAC,eAC3G,CAAA,OAAOC,WAAW,cACf,qEACA,EAAC,IAJF,qBAAA;uBAAA;4BAAA;8BAAA;YAKP;QACF;QAEA,sCAAsC;QACtC,MAAMC,qBAAsD;YAC1DjE,MAAM;QACR;QACA,MAAMkE,gBAAqCC,OAAOC,IAAI,CACpDH;QAEFC,cAAcG,OAAO,CAAC,CAACR;YACrB,IAAIA,QAAQ,QAAQ;gBAClB,IACEvC,KAAK,CAACuC,IAAI,IAAI,QACb,OAAOvC,KAAK,CAACuC,IAAI,KAAK,YAAY,OAAOvC,KAAK,CAACuC,IAAI,KAAK,UACzD;oBACA,MAAMH,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQzC,KAAK,CAACuC,IAAI,KAAK,OAAO,SAAS,OAAOvC,KAAK,CAACuC,IAAI;oBAC1D;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMS,IAAWT;YACnB;QACF;QAEA,sCAAsC;QACtC,MAAMU,qBAAsD;YAC1DtE,IAAI;YACJE,SAAS;YACTC,QAAQ;YACR+B,SAAS;YACTD,UAAU;YACVF,UAAU;YACVY,yBAAyB;YACzBR,SAAS;YACTC,cAAc;YACdE,cAAc;YACdE,gBAAgB;YAChBpC,YAAY;QACd;QACA,MAAMmE,gBAAqCL,OAAOC,IAAI,CACpDG;QAEFC,cAAcH,OAAO,CAAC,CAACR;YACrB,MAAMY,UAAU,OAAOnD,KAAK,CAACuC,IAAI;YAEjC,IAAIA,QAAQ,MAAM;gBAChB,IAAIvC,KAAK,CAACuC,IAAI,IAAIY,YAAY,YAAYA,YAAY,UAAU;oBAC9D,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IACLZ,QAAQ,aACRA,QAAQ,kBACRA,QAAQ,kBACRA,QAAQ,cACR;gBACA,IAAIvC,KAAK,CAACuC,IAAI,IAAIY,YAAY,YAAY;oBACxC,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IACLZ,QAAQ,aACRA,QAAQ,YACRA,QAAQ,aACRA,QAAQ,cACRA,QAAQ,cACRA,QAAQ,oBACRA,QAAQ,2BACR;gBACA,IAAIvC,KAAK,CAACuC,IAAI,IAAI,QAAQY,YAAY,WAAW;oBAC/C,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMH,IAAWT;YACnB;QACF;IACF;IAEA,IAAIN,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAInC,MAAMoD,MAAM,EAAE;YAChBC,CAAAA,GAAAA,UAAAA,QAAQ,EACN;QAEJ;QACA,IAAI,CAAC7C,QAAQ;YACX,IAAI9B;YACJ,IAAI,OAAO6B,aAAa,UAAU;gBAChC7B,OAAO6B;YACT,OAAO,IACL,OAAOA,aAAa,YACpB,OAAOA,SAAS+C,QAAQ,KAAK,UAC7B;gBACA5E,OAAO6B,SAAS+C,QAAQ;YAC1B;YAEA,IAAI5E,MAAM;gBACR,MAAM6E,oBAAoB7E,KACvB8E,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UAAYA,QAAQC,UAAU,CAAC,QAAQD,QAAQE,QAAQ,CAAC;gBAEjE,IAAIL,mBAAmB;oBACrB,MAAM,OAAA,cAEL,CAFK,IAAIjB,MACP,mBAAiB5D,OAAK,6IADnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;IACF;IAEA,MAAM,EAAEA,IAAI,EAAEC,EAAE,EAAE,GAAGgB,OAAAA,OAAK,CAACkE,OAAO;iCAAC;YACjC,MAAMC,eAAejE,kBAAkBU;YACvC,OAAO;gBACL7B,MAAMoF;gBACNnF,IAAI6B,SAASX,kBAAkBW,UAAUsD;YAC3C;QACF;gCAAG;QAACvD;QAAUC;KAAO;IAErB,oFAAoF;IACpF,IAAIuD;IACJ,IAAI5C,gBAAgB;QAClB,IAAIc,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,IAAIrB,SAAS;gBACXkD,QAAQC,IAAI,CACT,oDAAoD1D,WAAS;YAElE;YACA,IAAIS,kBAAkB;gBACpBgD,QAAQC,IAAI,CACT,yDAAyD1D,WAAS;YAEvE;YACA,IAAI;gBACFwD,QAAQpE,OAAAA,OAAK,CAACuE,QAAQ,CAACC,IAAI,CAAC9D;YAC9B,EAAE,OAAO+D,KAAK;gBACZ,IAAI,CAAC/D,UAAU;oBACb,MAAM,OAAA,cAEL,CAFK,IAAIiC,MACP,uDAAuD/B,WAAS,kFAD7D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,MAAM,OAAA,cAKL,CALK,IAAI+B,MACP,6DAA6D/B,WAAS,8FACpE,CAAA,OAAOmC,WAAW,cACf,sEACA,EAAC,IAJH,qBAAA;2BAAA;gCAAA;kCAAA;gBAKN;YACF;QACF,OAAO;;QAEP;IACF,OAAO;QACL,IAAIT,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,IAAI,CAAC9B,YAAAA,OAAAA,KAAAA,IAAAA,SAAkBgE,IAAI,MAAK,KAAK;gBACnC,MAAM,OAAA,cAEL,CAFK,IAAI/B,MACR,oKADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,MAAMgC,WAAgBnD,iBAClB4C,SAAS,OAAOA,UAAU,YAAYA,MAAM3C,GAAG,GAC/CC;IAEJ,4EAA4E;IAC5E,sEAAsE;IACtE,4EAA4E;IAC5E,6BAA6B;IAC7B,MAAMkD,+BAA+B5E,OAAAA,OAAK,CAAC6E,WAAW;mEACpD,CAACC;YACC,IAAIhD,WAAW,MAAM;gBACnB7C,gBAAgBc,OAAO,GAAGgF,CAAAA,GAAAA,OAAAA,iBAAiB,EACzCD,SACA/F,MACA+C,QACAI,iBACAD,iBACA1B;YAEJ;YAEA;2EAAO;oBACL,IAAItB,gBAAgBc,OAAO,EAAE;wBAC3BiF,CAAAA,GAAAA,OAAAA,+BAA+B,EAAC/F,gBAAgBc,OAAO;wBACvDd,gBAAgBc,OAAO,GAAG;oBAC5B;oBACAkF,CAAAA,GAAAA,OAAAA,2BAA2B,EAACH;gBAC9B;;QACF;kEACA;QAAC7C;QAAiBlD;QAAM+C;QAAQI;QAAiB3B;KAAwB;IAG3E,MAAM2E,YAAYC,CAAAA,GAAAA,cAAAA,YAAY,EAACP,8BAA8BD;IAE7D,MAAMS,aAMF;QACF3D,KAAKyD;QACL/D,SAAQrC,CAAC;YACP,IAAIwD,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;gBACzC,IAAI,CAAC1D,GAAG;oBACN,MAAM,OAAA,cAEL,CAFK,IAAI6D,MACP,mFADG,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,IAAI,CAACnB,kBAAkB,OAAOL,YAAY,YAAY;gBACpDA,QAAQrC;YACV;YAEA,IACE0C,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACc,OAAO,KAAK,YAC/B;gBACAiD,MAAM/D,KAAK,CAACc,OAAO,CAACrC;YACtB;YAEA,IAAI,CAACgD,QAAQ;gBACX;YACF;YAEA,IAAIhD,EAAEuG,gBAAgB,EAAE;gBACtB;YACF;YAEAxG,YAAYC,GAAGC,MAAMC,IAAIC,iBAAiBC,SAASC,QAAQC;QAC7D;QACAgC,cAAatC,CAAC;YACZ,IAAI,CAAC0C,kBAAkB,OAAOH,qBAAqB,YAAY;gBAC7DA,iBAAiBvC;YACnB;YAEA,IACE0C,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACe,YAAY,KAAK,YACpC;gBACAgD,MAAM/D,KAAK,CAACe,YAAY,CAACtC;YAC3B;YAEA,IAAI,CAACgD,QAAQ;gBACX;YACF;YAEA,IAAI,CAACG,mBAAmBK,QAAQC,GAAG,CAACC,IAA4B,IAApB,KAAK;gBAC/C;YACF;;YAEA,MAAM8C,2BAA2B3D,4BAA4B;QAK/D;QACAL,cAAcgB,QAAQC,GAAG,CAACiD,0BAA0B,GAChDC,oCACA,SAASnE,aAAaxC,CAAC;YACrB,IAAI,CAAC0C,kBAAkB,OAAOD,qBAAqB,YAAY;gBAC7DA,iBAAiBzC;YACnB;YAEA,IACE0C,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACiB,YAAY,KAAK,YACpC;gBACA8C,MAAM/D,KAAK,CAACiB,YAAY,CAACxC;YAC3B;YAEA,IAAI,CAACgD,QAAQ;gBACX;YACF;YAEA,IAAI,CAACG,iBAAiB;gBACpB;YACF;YAEA,MAAMqD,2BAA2B3D,4BAA4B;YAC7D4D,CAAAA,GAAAA,OAAAA,kBAAkB,EAChBzG,EAAEV,aAAa,EACfkH;QAEJ;IACN;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,2EAA2E;IAC3E,IAAII,CAAAA,GAAAA,OAAAA,aAAa,EAAC1G,KAAK;QACrBoG,WAAWrG,IAAI,GAAGC;IACpB,OAAO,IACL,CAACwC,kBACDP,YACCmD,MAAMM,IAAI,KAAK,OAAO,CAAE,CAAA,UAAUN,MAAM/D,KAAI,GAC7C;QACA+E,WAAWrG,IAAI,GAAG4G,CAAAA,GAAAA,aAAAA,WAAW,EAAC3G;IAChC;IAEA,IAAI4G;IAEJ,IAAIpE,gBAAgB;QAClB,IAAIc,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1CqD,CAAAA,GAAAA,WAAAA,SAAS,EACP,oEACE,oEACA,4CACA;QAEN;QACAD,OAAAA,WAAAA,GAAO5F,OAAAA,OAAK,CAAC8F,YAAY,CAAC1B,OAAOgB;IACnC,OAAO;QACLQ,OAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAAC/D,KAAAA;YAAG,GAAGD,SAAS;YAAG,GAAGwD,UAAU;sBAC7B1E;;IAGP;IAEA,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACqF,kBAAkBC,QAAQ,EAAA;QAACC,OAAO3F;kBAChCsF;;AAGP;AAEA,MAAMG,oBAAAA,WAAAA,GAAoBG,CAAAA,GAAAA,OAAAA,aAAa,EAErCzF,OAAAA,gBAAgB;AAEX,MAAMzC,gBAAgB;IAC3B,OAAO+D,CAAAA,GAAAA,OAAAA,UAAU,EAACgE;AACpB", "ignoreList": [0], "debugId": null}}]}