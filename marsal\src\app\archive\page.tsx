'use client'

import React, { useState, useEffect } from 'react'
import { useAuth } from '@/components/auth-provider'
import { Header } from '@/components/header'
import { Navigation } from '@/components/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { 
  Archive, 
  Search, 
  Filter, 
  Eye, 
  Download, 
  Calendar,
  Package,
  Phone,
  MapPin,
  DollarSign,
  User,
  Clock,
  FileText
} from 'lucide-react'
import { getOrders, Order } from '@/lib/storage'
import { formatCurrency, formatDate, shareOnWhatsApp } from '@/lib/utils'

export default function ArchivePage() {
  const { user } = useAuth()
  const [orders, setOrders] = useState<Order[]>([])
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [dateFilter, setDateFilter] = useState<string>('all')
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false)

  useEffect(() => {
    loadArchivedOrders()
  }, [user])

  useEffect(() => {
    filterOrders()
  }, [orders, searchTerm, dateFilter])

  const loadArchivedOrders = () => {
    const allOrders = getOrders()
    let archivedOrders = allOrders.filter(order => 
      order.status === 'delivered' || order.status === 'cancelled'
    )

    // فلترة حسب دور المستخدم
    if (user?.role === 'courier') {
      archivedOrders = archivedOrders.filter(order => order.assignedTo === user.id)
    }

    // ترتيب حسب التاريخ (الأحدث أولاً)
    archivedOrders.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
    
    setOrders(archivedOrders)
  }

  const filterOrders = () => {
    let filtered = orders

    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customerPhone.includes(searchTerm) ||
        order.id.includes(searchTerm) ||
        order.customerAddress.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (dateFilter !== 'all') {
      const now = new Date()
      const filterDate = new Date()

      switch (dateFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0)
          filtered = filtered.filter(order => new Date(order.updatedAt) >= filterDate)
          break
        case 'week':
          filterDate.setDate(now.getDate() - 7)
          filtered = filtered.filter(order => new Date(order.updatedAt) >= filterDate)
          break
        case 'month':
          filterDate.setMonth(now.getMonth() - 1)
          filtered = filtered.filter(order => new Date(order.updatedAt) >= filterDate)
          break
        case 'quarter':
          filterDate.setMonth(now.getMonth() - 3)
          filtered = filtered.filter(order => new Date(order.updatedAt) >= filterDate)
          break
      }
    }

    setFilteredOrders(filtered)
  }

  const handleViewDetails = (order: Order) => {
    setSelectedOrder(order)
    setIsDetailsDialogOpen(true)
  }

  const exportToCSV = () => {
    const csvContent = [
      ['رقم الطلب', 'اسم العميل', 'رقم الهاتف', 'العنوان', 'المبلغ', 'الحالة', 'تاريخ الإنشاء', 'تاريخ التحديث'],
      ...filteredOrders.map(order => [
        order.id,
        order.customerName,
        order.customerPhone,
        order.customerAddress,
        order.totalAmount.toString(),
        order.status === 'delivered' ? 'تم التسليم' : 'ملغي',
        formatDate(order.createdAt),
        formatDate(order.updatedAt)
      ])
    ].map(row => row.join(',')).join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `archive_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const getStatusBadge = (status: string) => {
    if (status === 'delivered') {
      return <Badge className="bg-green-100 text-green-800">تم التسليم</Badge>
    } else if (status === 'cancelled') {
      return <Badge className="bg-gray-100 text-gray-800">ملغي</Badge>
    }
    return null
  }

  const getOrderStats = () => {
    const delivered = filteredOrders.filter(o => o.status === 'delivered').length
    const cancelled = filteredOrders.filter(o => o.status === 'cancelled').length
    const totalRevenue = filteredOrders
      .filter(o => o.status === 'delivered')
      .reduce((sum, o) => sum + o.totalAmount, 0)

    return { delivered, cancelled, totalRevenue }
  }

  const stats = getOrderStats()

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="flex">
        <Navigation />
        
        <main className="flex-1 p-6">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">أرشيف الطلبات</h1>
            <p className="text-gray-600">عرض الطلبات المكتملة والملغية</p>
          </div>

          {/* إحصائيات سريعة */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Archive className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                    <p className="text-2xl font-bold text-gray-900">{filteredOrders.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Package className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">تم التسليم</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.delivered}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-gray-100 rounded-lg">
                    <Package className="h-6 w-6 text-gray-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">ملغي</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.cancelled}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <DollarSign className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">إجمالي الإيرادات</p>
                    <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.totalRevenue)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* أدوات التحكم */}
          <div className="mb-6 flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="البحث في الأرشيف..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            
            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger className="w-48">
                <Calendar className="h-4 w-4 ml-2" />
                <SelectValue placeholder="فلترة حسب التاريخ" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع التواريخ</SelectItem>
                <SelectItem value="today">اليوم</SelectItem>
                <SelectItem value="week">آخر أسبوع</SelectItem>
                <SelectItem value="month">آخر شهر</SelectItem>
                <SelectItem value="quarter">آخر 3 أشهر</SelectItem>
              </SelectContent>
            </Select>

            <Button onClick={exportToCSV} variant="outline">
              <Download className="h-4 w-4 ml-2" />
              تصدير CSV
            </Button>
          </div>

          {/* قائمة الطلبات المؤرشفة */}
          <div className="grid gap-4">
            {filteredOrders.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <Archive className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">لا توجد طلبات مؤرشفة</h3>
                  <p className="text-gray-600">لم يتم العثور على أي طلبات تطابق معايير البحث</p>
                </CardContent>
              </Card>
            ) : (
              filteredOrders.map((order) => (
                <Card key={order.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">
                          {order.customerName}
                        </h3>
                        <p className="text-sm text-gray-600">رقم الطلب: {order.id}</p>
                      </div>
                      {getStatusBadge(order.status)}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                      <div className="flex items-center text-sm text-gray-600">
                        <Phone className="h-4 w-4 ml-2" />
                        {order.customerPhone}
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <MapPin className="h-4 w-4 ml-2" />
                        {order.customerAddress.substring(0, 30)}...
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <DollarSign className="h-4 w-4 ml-2" />
                        {formatCurrency(order.totalAmount)}
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Clock className="h-4 w-4 ml-2" />
                        {formatDate(order.updatedAt)}
                      </div>
                    </div>
                    
                    {order.items && (
                      <div className="mb-4">
                        <p className="text-sm text-gray-600">
                          <strong>المنتجات:</strong> {order.items}
                        </p>
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between">
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewDetails(order)}
                        >
                          <Eye className="h-4 w-4 ml-1" />
                          عرض التفاصيل
                        </Button>
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => shareOnWhatsApp(order.customerPhone, `شكراً لك ${order.customerName}، تم ${order.status === 'delivered' ? 'تسليم' : 'إلغاء'} طلبكم رقم ${order.id}`)}
                        >
                          <Phone className="h-4 w-4 ml-1" />
                          واتساب
                        </Button>
                      </div>
                      
                      <div className="text-sm text-gray-500">
                        تاريخ الإنشاء: {formatDate(order.createdAt)}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>

          {/* نافذة تفاصيل الطلب */}
          <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>تفاصيل الطلب</DialogTitle>
                <DialogDescription>
                  عرض تفاصيل الطلب وتاريخ الحالات
                </DialogDescription>
              </DialogHeader>
              
              {selectedOrder && (
                <div className="space-y-6">
                  {/* معلومات العميل */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3">معلومات العميل</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-600">الاسم</p>
                        <p className="font-medium">{selectedOrder.customerName}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">رقم الهاتف</p>
                        <p className="font-medium">{selectedOrder.customerPhone}</p>
                      </div>
                      <div className="col-span-2">
                        <p className="text-sm text-gray-600">العنوان</p>
                        <p className="font-medium">{selectedOrder.customerAddress}</p>
                      </div>
                    </div>
                  </div>

                  {/* تفاصيل الطلب */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3">تفاصيل الطلب</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-600">رقم الطلب</p>
                        <p className="font-medium">{selectedOrder.id}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">المبلغ الإجمالي</p>
                        <p className="font-medium">{formatCurrency(selectedOrder.totalAmount)}</p>
                      </div>
                      {selectedOrder.items && (
                        <div className="col-span-2">
                          <p className="text-sm text-gray-600">المنتجات</p>
                          <p className="font-medium">{selectedOrder.items}</p>
                        </div>
                      )}
                      {selectedOrder.notes && (
                        <div className="col-span-2">
                          <p className="text-sm text-gray-600">ملاحظات</p>
                          <p className="font-medium">{selectedOrder.notes}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* تاريخ الحالات */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3">تاريخ الحالات</h3>
                    <div className="space-y-3">
                      {selectedOrder.statusHistory.map((history, index) => (
                        <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                          <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                          <div className="flex-1">
                            <p className="font-medium">
                              {history.status === 'delivered' ? 'تم التسليم' : 
                               history.status === 'cancelled' ? 'ملغي' : 
                               history.status}
                            </p>
                            <p className="text-sm text-gray-600">{formatDate(history.timestamp)}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </DialogContent>
          </Dialog>
        </main>
      </div>
    </div>
  )
}
