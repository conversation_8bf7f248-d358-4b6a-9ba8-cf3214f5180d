'use client'

import React, { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useAuth } from '@/components/auth-provider'
import { Header } from '@/components/header'
import { Navigation } from '@/components/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { getOrders, getStatusLabel } from '@/lib/storage'
import { Order } from '@/types'
import { ArrowRight, Edit, Truck, Phone, MapPin, Calendar, User, Package, DollarSign, RefreshCw } from 'lucide-react'
import { formatCurrency, formatDate } from '@/lib/utils'
import { OrderStatusUpdate } from '@/components/order-status-update'

export default function OrderDetailsPage() {
  const { user, isLoading } = useAuth()
  const router = useRouter()
  const params = useParams()
  const [order, setOrder] = useState<Order | null>(null)
  const [isLoadingOrder, setIsLoadingOrder] = useState(true)
  const [showStatusUpdate, setShowStatusUpdate] = useState(false)

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/login')
      return
    }

    if (user && params.id) {
      loadOrder(params.id as string)
    }
  }, [user, isLoading, router, params.id])

  const loadOrder = (orderId: string) => {
    setIsLoadingOrder(true)
    try {
      const orders = getOrders()
      const foundOrder = orders.find(o => o.id === orderId)
      
      if (!foundOrder) {
        router.push('/orders')
        return
      }

      // التحقق من الصلاحيات
      if (user?.role === 'courier' && foundOrder.courierId !== user.id) {
        router.push('/orders')
        return
      }

      setOrder(foundOrder)
    } catch (error) {
      console.error('Error loading order:', error)
      router.push('/orders')
    } finally {
      setIsLoadingOrder(false)
    }
  }

  const handleStatusUpdate = () => {
    // إعادة تحميل الطلب بعد التحديث
    if (params.id) {
      loadOrder(params.id as string)
    }
    setShowStatusUpdate(false)
  }

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      processing: 'bg-blue-100 text-blue-800 border-blue-200',
      shipped: 'bg-purple-100 text-purple-800 border-purple-200',
      out_for_delivery: 'bg-orange-100 text-orange-800 border-orange-200',
      delivered: 'bg-green-100 text-green-800 border-green-200',
      returned: 'bg-red-100 text-red-800 border-red-200',
      cancelled: 'bg-gray-100 text-gray-800 border-gray-200',
      postponed: 'bg-indigo-100 text-indigo-800 border-indigo-200',
    }
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200'
  }

  if (isLoading || isLoadingOrder) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!user || !order) return null

  const canEdit = user.role === 'manager' || user.role === 'supervisor' || 
                  (user.role === 'courier' && order.courierId === user.id)

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="flex">
        <Navigation />
        
        <main className="flex-1 p-6">
          {/* العنوان والإجراءات */}
          <div className="flex justify-between items-start mb-6">
            <div className="flex items-center space-x-4 space-x-reverse">
              <Button
                variant="outline"
                onClick={() => router.back()}
                className="flex items-center space-x-2 space-x-reverse"
              >
                <ArrowRight className="h-4 w-4" />
                <span>رجوع</span>
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">تفاصيل الطلب</h1>
                <p className="text-gray-600 mt-1">{order.trackingNumber}</p>
              </div>
            </div>
            
            {canEdit && (
              <div className="flex space-x-2 space-x-reverse">
                <Button
                  variant="outline"
                  onClick={() => setShowStatusUpdate(true)}
                  className="flex items-center space-x-2 space-x-reverse"
                >
                  <RefreshCw className="h-4 w-4" />
                  <span>تحديث سريع</span>
                </Button>
                <Button
                  onClick={() => router.push(`/orders/${order.id}/edit`)}
                  className="flex items-center space-x-2 space-x-reverse"
                >
                  <Edit className="h-4 w-4" />
                  <span>تعديل</span>
                </Button>
              </div>
            )}
          </div>

          {/* مكون التحديث السريع */}
          {showStatusUpdate && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
              <OrderStatusUpdate
                order={order}
                currentUser={user.name}
                onUpdate={handleStatusUpdate}
                onCancel={() => setShowStatusUpdate(false)}
              />
            </div>
          )}

          <div className="grid gap-6 max-w-6xl">
            {/* معلومات أساسية */}
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-xl">معلومات الطلب</CardTitle>
                    <CardDescription>البيانات الأساسية للطلب</CardDescription>
                  </div>
                  <div className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(order.status)}`}>
                    {getStatusLabel(order.status as any)}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Package className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">رقم التتبع</p>
                      <p className="font-semibold">{order.trackingNumber}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <DollarSign className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">المبلغ</p>
                      <p className="font-semibold">{formatCurrency(order.amount)}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <Calendar className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">تاريخ الإنشاء</p>
                      <p className="font-semibold">{formatDate(order.createdAt)}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <Truck className="h-5 w-5 text-orange-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">المندوب</p>
                      <p className="font-semibold">{order.courierName || 'غير محدد'}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* بيانات المرسل والمستلم */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* بيانات المرسل */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2 space-x-reverse">
                    <User className="h-5 w-5" />
                    <span>بيانات المرسل</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-600">الاسم</p>
                    <p className="font-semibold">{order.senderName}</p>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">رقم الهاتف</p>
                      <p className="font-semibold">{order.senderPhone}</p>
                    </div>
                  </div>
                  {order.senderAddress && (
                    <div className="flex items-start space-x-2 space-x-reverse">
                      <MapPin className="h-4 w-4 text-gray-400 mt-1" />
                      <div>
                        <p className="text-sm text-gray-600">العنوان</p>
                        <p className="font-semibold">{order.senderAddress}</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* بيانات المستلم */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2 space-x-reverse">
                    <User className="h-5 w-5" />
                    <span>بيانات المستلم</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-600">الاسم</p>
                    <p className="font-semibold">{order.recipientName}</p>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">رقم الهاتف</p>
                      <p className="font-semibold">{order.recipientPhone}</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-2 space-x-reverse">
                    <MapPin className="h-4 w-4 text-gray-400 mt-1" />
                    <div>
                      <p className="text-sm text-gray-600">العنوان</p>
                      <p className="font-semibold">{order.recipientAddress}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* تفاصيل إضافية */}
            {(order.description || order.notes) && (
              <Card>
                <CardHeader>
                  <CardTitle>تفاصيل إضافية</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {order.description && (
                    <div>
                      <p className="text-sm text-gray-600 mb-1">وصف الطلب</p>
                      <p className="font-semibold">{order.description}</p>
                    </div>
                  )}
                  {order.notes && (
                    <div>
                      <p className="text-sm text-gray-600 mb-1">ملاحظات</p>
                      <p className="font-semibold">{order.notes}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* تاريخ الحالات */}
            <Card>
              <CardHeader>
                <CardTitle>تاريخ الحالات</CardTitle>
                <CardDescription>سجل تحديثات حالة الطلب</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {order.statusHistory.map((update, index) => (
                    <div key={update.id} className="flex items-start space-x-4 space-x-reverse">
                      <div className={`w-3 h-3 rounded-full mt-2 ${index === 0 ? 'bg-blue-500' : 'bg-gray-300'}`} />
                      <div className="flex-1">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-semibold">{getStatusLabel(update.status)}</p>
                            {update.notes && (
                              <p className="text-sm text-gray-600 mt-1">{update.notes}</p>
                            )}
                          </div>
                          <div className="text-left">
                            <p className="text-sm text-gray-500">{formatDate(update.updatedAt)}</p>
                            <p className="text-xs text-gray-400">{update.updatedBy}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  )
}
