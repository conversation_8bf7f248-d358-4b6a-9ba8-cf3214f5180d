"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/cards/section-card.tsx":
/*!***********************************************!*\
  !*** ./src/components/cards/section-card.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SectionCard: () => (/* binding */ SectionCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ SectionCard auto */ \n\n\n\nconst colorClasses = {\n    blue: {\n        gradient: 'from-blue-500 to-blue-600',\n        bg: 'bg-blue-50',\n        text: 'text-blue-600',\n        border: 'border-blue-200',\n        hover: 'hover:border-blue-300'\n    },\n    green: {\n        gradient: 'from-green-500 to-green-600',\n        bg: 'bg-green-50',\n        text: 'text-green-600',\n        border: 'border-green-200',\n        hover: 'hover:border-green-300'\n    },\n    purple: {\n        gradient: 'from-purple-500 to-purple-600',\n        bg: 'bg-purple-50',\n        text: 'text-purple-600',\n        border: 'border-purple-200',\n        hover: 'hover:border-purple-300'\n    },\n    orange: {\n        gradient: 'from-orange-500 to-orange-600',\n        bg: 'bg-orange-50',\n        text: 'text-orange-600',\n        border: 'border-orange-200',\n        hover: 'hover:border-orange-300'\n    },\n    red: {\n        gradient: 'from-red-500 to-red-600',\n        bg: 'bg-red-50',\n        text: 'text-red-600',\n        border: 'border-red-200',\n        hover: 'hover:border-red-300'\n    },\n    indigo: {\n        gradient: 'from-indigo-500 to-indigo-600',\n        bg: 'bg-indigo-50',\n        text: 'text-indigo-600',\n        border: 'border-indigo-200',\n        hover: 'hover:border-indigo-300'\n    },\n    pink: {\n        gradient: 'from-pink-500 to-pink-600',\n        bg: 'bg-pink-50',\n        text: 'text-pink-600',\n        border: 'border-pink-200',\n        hover: 'hover:border-pink-300'\n    },\n    teal: {\n        gradient: 'from-teal-500 to-teal-600',\n        bg: 'bg-teal-50',\n        text: 'text-teal-600',\n        border: 'border-teal-200',\n        hover: 'hover:border-teal-300'\n    }\n};\nfunction SectionCard(param) {\n    let { title, description, icon: Icon, href, color, count, trend } = param;\n    const colors = colorClasses[color] || colorClasses.blue // fallback to blue if color not found\n    ;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n        href: href,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: \"group cursor-pointer transition-all duration-300 hover-lift border-2 \".concat(colors.border, \" \").concat(colors.hover, \" \").concat(colors.bg, \" hover:shadow-strong animate-fade-in\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 space-x-reverse mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 rounded-xl bg-gradient-to-br \".concat(colors.gradient, \" flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold \".concat(colors.text, \" group-hover:text-opacity-80 transition-colors\"),\n                                                children: title\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 19\n                                            }, this),\n                                            count !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 space-x-reverse mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl font-bold \".concat(colors.text),\n                                                        children: count.toLocaleString('ar-EG')\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium \".concat(trend.isPositive ? 'text-green-600' : 'text-red-600'),\n                                                        children: [\n                                                            trend.isPositive ? '+' : '',\n                                                            trend.value,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 text-sm leading-relaxed mb-4\",\n                                children: description\n                            }, void 0, false, {\n                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center text-sm font-medium \".concat(colors.text, \" group-hover:translate-x-1 transition-transform duration-200\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"عرض التفاصيل\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M15 19l-7-7 7-7\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n                lineNumber: 86,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n            lineNumber: 85,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\مندوب\\\\marsal\\\\src\\\\components\\\\cards\\\\section-card.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, this);\n}\n_c = SectionCard;\nvar _c;\n$RefreshReg$(_c, \"SectionCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/cards/section-card.tsx\n"));

/***/ })

});