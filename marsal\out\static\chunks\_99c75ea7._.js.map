{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/src/lib/auth.ts"], "sourcesContent": ["import { User, UserRole } from '@/types'\n\n// المستخدمون الافتراضيون\nconst defaultUsers: User[] = [\n  {\n    id: '1',\n    name: 'مدير النظام',\n    username: 'manager',\n    password: '123456',\n    phone: '07901234567',\n    role: 'manager',\n    isActive: true,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '2',\n    name: 'المتابع الأول',\n    username: 'supervisor',\n    password: '123456',\n    phone: '07901234568',\n    role: 'supervisor',\n    isActive: true,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '3',\n    name: 'المندوب الأول',\n    username: 'courier',\n    password: '123456',\n    phone: '07901234569',\n    role: 'courier',\n    isActive: true,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n]\n\n// تهيئة المستخدمين الافتراضيين\nexport function initializeDefaultUsers(): void {\n  if (typeof window === 'undefined') return\n  \n  const existingUsers = localStorage.getItem('marsal_users')\n  if (!existingUsers) {\n    localStorage.setItem('marsal_users', JSON.stringify(defaultUsers))\n  }\n}\n\n// تسجيل الدخول\nexport function login(username: string, password: string): User | null {\n  if (typeof window === 'undefined') return null\n  \n  const users = getUsers()\n  const user = users.find(u => u.username === username && u.password === password && u.isActive)\n  \n  if (user) {\n    // حفظ بيانات الجلسة\n    const session = {\n      userId: user.id,\n      username: user.username,\n      role: user.role,\n      loginTime: new Date().toISOString(),\n      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 ساعة\n    }\n    localStorage.setItem('marsal_session', JSON.stringify(session))\n    return user\n  }\n  \n  return null\n}\n\n// تسجيل الخروج\nexport function logout(): void {\n  if (typeof window === 'undefined') return\n  localStorage.removeItem('marsal_session')\n}\n\n// التحقق من الجلسة\nexport function getCurrentUser(): User | null {\n  if (typeof window === 'undefined') return null\n  \n  const sessionData = localStorage.getItem('marsal_session')\n  if (!sessionData) return null\n  \n  try {\n    const session = JSON.parse(sessionData)\n    const now = new Date()\n    const expiresAt = new Date(session.expiresAt)\n    \n    if (now > expiresAt) {\n      logout()\n      return null\n    }\n    \n    const users = getUsers()\n    const user = users.find(u => u.id === session.userId && u.isActive)\n    return user || null\n  } catch {\n    logout()\n    return null\n  }\n}\n\n// التحقق من صحة الجلسة\nexport function isAuthenticated(): boolean {\n  return getCurrentUser() !== null\n}\n\n// التحقق من الدور\nexport function hasRole(role: UserRole): boolean {\n  const user = getCurrentUser()\n  return user?.role === role\n}\n\n// التحقق من الصلاحية\nexport function hasPermission(permission: string): boolean {\n  const user = getCurrentUser()\n  if (!user) return false\n  \n  // المدير له جميع الصلاحيات\n  if (user.role === 'manager') return true\n  \n  // تحديد الصلاحيات حسب الدور\n  const permissions: Record<UserRole, string[]> = {\n    manager: ['*'], // جميع الصلاحيات\n    supervisor: [\n      'view_orders',\n      'create_orders',\n      'edit_orders',\n      'view_statistics',\n      'manage_users',\n      'view_archive',\n      'manage_notifications',\n      'manage_settings',\n      'return_delivered_orders', // صلاحية خاصة\n    ],\n    courier: [\n      'view_orders',\n      'create_orders',\n      'edit_orders',\n      'view_statistics',\n      'view_archive_own',\n      'manage_notifications',\n      'manage_settings',\n    ],\n  }\n  \n  const userPermissions = permissions[user.role] || []\n  return userPermissions.includes('*') || userPermissions.includes(permission)\n}\n\n// الحصول على جميع المستخدمين\nexport function getUsers(): User[] {\n  if (typeof window === 'undefined') return []\n  \n  const usersData = localStorage.getItem('marsal_users')\n  if (!usersData) {\n    initializeDefaultUsers()\n    return defaultUsers\n  }\n  \n  try {\n    return JSON.parse(usersData)\n  } catch {\n    return defaultUsers\n  }\n}\n\n// إضافة مستخدم جديد\nexport function addUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): User | null {\n  if (typeof window === 'undefined') return null\n  \n  const users = getUsers()\n  \n  // التحقق من تكرار اسم المستخدم\n  if (users.some(u => u.username === userData.username)) {\n    return null\n  }\n  \n  const newUser: User = {\n    ...userData,\n    id: Date.now().toString(),\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  }\n  \n  users.push(newUser)\n  localStorage.setItem('marsal_users', JSON.stringify(users))\n  \n  return newUser\n}\n\n// تحديث مستخدم\nexport function updateUser(userId: string, updates: Partial<User>): boolean {\n  if (typeof window === 'undefined') return false\n  \n  const users = getUsers()\n  const userIndex = users.findIndex(u => u.id === userId)\n  \n  if (userIndex === -1) return false\n  \n  users[userIndex] = {\n    ...users[userIndex],\n    ...updates,\n    updatedAt: new Date().toISOString(),\n  }\n  \n  localStorage.setItem('marsal_users', JSON.stringify(users))\n  return true\n}\n\n// حذف مستخدم\nexport function deleteUser(userId: string): boolean {\n  if (typeof window === 'undefined') return false\n  \n  const users = getUsers()\n  const filteredUsers = users.filter(u => u.id !== userId)\n  \n  if (filteredUsers.length === users.length) return false\n  \n  localStorage.setItem('marsal_users', JSON.stringify(filteredUsers))\n  return true\n}\n\n// الحصول على المندوبين فقط\nexport function getCouriers(): User[] {\n  return getUsers().filter(u => u.role === 'courier' && u.isActive)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA,yBAAyB;AACzB,MAAM,eAAuB;IAC3B;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;CACD;AAGM,SAAS;IACd,uCAAmC;;IAAK;IAExC,MAAM,gBAAgB,aAAa,OAAO,CAAC;IAC3C,IAAI,CAAC,eAAe;QAClB,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;IACtD;AACF;AAGO,SAAS,MAAM,QAAgB,EAAE,QAAgB;IACtD,uCAAmC;;IAAU;IAE7C,MAAM,QAAQ;IACd,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,YAAY,EAAE,QAAQ,KAAK,YAAY,EAAE,QAAQ;IAE7F,IAAI,MAAM;QACR,oBAAoB;QACpB,MAAM,UAAU;YACd,QAAQ,KAAK,EAAE;YACf,UAAU,KAAK,QAAQ;YACvB,MAAM,KAAK,IAAI;YACf,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,MAAM,WAAW;QACnE;QACA,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;QACtD,OAAO;IACT;IAEA,OAAO;AACT;AAGO,SAAS;IACd,uCAAmC;;IAAK;IACxC,aAAa,UAAU,CAAC;AAC1B;AAGO,SAAS;IACd,uCAAmC;;IAAU;IAE7C,MAAM,cAAc,aAAa,OAAO,CAAC;IACzC,IAAI,CAAC,aAAa,OAAO;IAEzB,IAAI;QACF,MAAM,UAAU,KAAK,KAAK,CAAC;QAC3B,MAAM,MAAM,IAAI;QAChB,MAAM,YAAY,IAAI,KAAK,QAAQ,SAAS;QAE5C,IAAI,MAAM,WAAW;YACnB;YACA,OAAO;QACT;QAEA,MAAM,QAAQ;QACd,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,MAAM,IAAI,EAAE,QAAQ;QAClE,OAAO,QAAQ;IACjB,EAAE,OAAM;QACN;QACA,OAAO;IACT;AACF;AAGO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAGO,SAAS,QAAQ,IAAc;IACpC,MAAM,OAAO;IACb,OAAO,MAAM,SAAS;AACxB;AAGO,SAAS,cAAc,UAAkB;IAC9C,MAAM,OAAO;IACb,IAAI,CAAC,MAAM,OAAO;IAElB,2BAA2B;IAC3B,IAAI,KAAK,IAAI,KAAK,WAAW,OAAO;IAEpC,4BAA4B;IAC5B,MAAM,cAA0C;QAC9C,SAAS;YAAC;SAAI;QACd,YAAY;YACV;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;YACP;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,MAAM,kBAAkB,WAAW,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE;IACpD,OAAO,gBAAgB,QAAQ,CAAC,QAAQ,gBAAgB,QAAQ,CAAC;AACnE;AAGO,SAAS;IACd,uCAAmC;;IAAQ;IAE3C,MAAM,YAAY,aAAa,OAAO,CAAC;IACvC,IAAI,CAAC,WAAW;QACd;QACA,OAAO;IACT;IAEA,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,SAAS,QAAQ,QAAsD;IAC5E,uCAAmC;;IAAU;IAE7C,MAAM,QAAQ;IAEd,+BAA+B;IAC/B,IAAI,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,SAAS,QAAQ,GAAG;QACrD,OAAO;IACT;IAEA,MAAM,UAAgB;QACpB,GAAG,QAAQ;QACX,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,MAAM,IAAI,CAAC;IACX,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;IAEpD,OAAO;AACT;AAGO,SAAS,WAAW,MAAc,EAAE,OAAsB;IAC/D,uCAAmC;;IAAW;IAE9C,MAAM,QAAQ;IACd,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAEhD,IAAI,cAAc,CAAC,GAAG,OAAO;IAE7B,KAAK,CAAC,UAAU,GAAG;QACjB,GAAG,KAAK,CAAC,UAAU;QACnB,GAAG,OAAO;QACV,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;IACpD,OAAO;AACT;AAGO,SAAS,WAAW,MAAc;IACvC,uCAAmC;;IAAW;IAE9C,MAAM,QAAQ;IACd,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAEjD,IAAI,cAAc,MAAM,KAAK,MAAM,MAAM,EAAE,OAAO;IAElD,aAAa,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;IACpD,OAAO;AACT;AAGO,SAAS;IACd,OAAO,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,EAAE,QAAQ;AAClE", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/src/components/auth-provider.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\nimport { User } from '@/types'\nimport { getCurrentUser, initializeDefaultUsers } from '@/lib/auth'\n\ninterface AuthContextType {\n  user: User | null\n  isLoading: boolean\n  login: (user: User) => void\n  logout: () => void\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    // تهيئة المستخدمين الافتراضيين\n    initializeDefaultUsers()\n    \n    // التحقق من الجلسة الحالية\n    const currentUser = getCurrentUser()\n    setUser(currentUser)\n    setIsLoading(false)\n  }, [])\n\n  const login = (user: User) => {\n    setUser(user)\n  }\n\n  const logout = () => {\n    setUser(null)\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('marsal_session')\n    }\n  }\n\n  return (\n    <AuthContext.Provider value={{ user, isLoading, login, logout }}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAaA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,+BAA+B;YAC/B,CAAA,GAAA,qHAAA,CAAA,yBAAsB,AAAD;YAErB,2BAA2B;YAC3B,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;YACjC,QAAQ;YACR,aAAa;QACf;iCAAG,EAAE;IAEL,MAAM,QAAQ,CAAC;QACb,QAAQ;IACV;IAEA,MAAM,SAAS;QACb,QAAQ;QACR,wCAAmC;YACjC,aAAa,UAAU,CAAC;QAC1B;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAM;YAAW;YAAO;QAAO;kBAC3D;;;;;;AAGP;GA9BgB;KAAA;AAgCT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 505, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}