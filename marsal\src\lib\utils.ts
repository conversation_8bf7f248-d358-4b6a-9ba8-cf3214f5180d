import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// دوال مساعدة للتطبيق
export function formatDate(date: string | Date): string {
  const d = new Date(date)
  return d.toLocaleDateString('ar-IQ', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('ar-IQ', {
    style: 'currency',
    currency: 'IQD',
    minimumFractionDigits: 0
  }).format(amount)
}

export function generateTrackingNumber(): string {
  const prefix = 'MRS'
  const timestamp = Date.now().toString().slice(-6)
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `${prefix}${timestamp}${random}`
}

export function validatePhoneNumber(phone: string): boolean {
  // التحقق من أرقام الهواتف العراقية
  const iraqiPhoneRegex = /^(\+964|964|0)?(7[0-9]{9}|1[0-9]{9})$/
  return iraqiPhoneRegex.test(phone.replace(/\s/g, ''))
}

export function validateIraqiPhone(phone: string): boolean {
  return validatePhoneNumber(phone)
}

export function copyToClipboard(text: string): Promise<void> {
  return navigator.clipboard.writeText(text)
}

export function shareViaWhatsApp(text: string): void {
  const encodedText = encodeURIComponent(text)
  const url = `https://wa.me/?text=${encodedText}`
  window.open(url, '_blank')
}
