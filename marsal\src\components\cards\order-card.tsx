'use client'

import React from 'react'
import { Package, MapPin, User, Clock, DollarSign, Phone, Calendar, Truck } from 'lucide-react'
import { Card, CardContent, CardHeader } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'

interface OrderCardProps {
  id: string
  trackingNumber: string
  customerName: string
  customerPhone: string
  customerAddress: string
  amount: number
  status: 'pending' | 'confirmed' | 'picked_up' | 'in_transit' | 'out_for_delivery' | 'delivered' | 'cancelled' | 'returned'
  assignedTo?: string
  createdAt: string
  deliveryDate?: string
  notes?: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  onStatusChange?: (orderId: string, newStatus: string) => void
  onAssign?: (orderId: string) => void
}

export function OrderCard({
  id,
  trackingNumber,
  customerName,
  customerPhone,
  customerAddress,
  amount,
  status,
  assignedTo,
  createdAt,
  deliveryDate,
  notes,
  priority,
  onStatusChange,
  onAssign
}: OrderCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'confirmed':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'picked_up':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200'
      case 'in_transit':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'out_for_delivery':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'returned':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'في الانتظار'
      case 'confirmed':
        return 'مؤكد'
      case 'picked_up':
        return 'تم الاستلام'
      case 'in_transit':
        return 'في الطريق'
      case 'out_for_delivery':
        return 'خارج للتوصيل'
      case 'delivered':
        return 'تم التوصيل'
      case 'cancelled':
        return 'ملغي'
      case 'returned':
        return 'مرجع'
      default:
        return status
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-500 text-white'
      case 'high':
        return 'bg-orange-500 text-white'
      case 'medium':
        return 'bg-yellow-500 text-white'
      case 'low':
        return 'bg-green-500 text-white'
      default:
        return 'bg-gray-500 text-white'
    }
  }

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'عاجل'
      case 'high':
        return 'عالي'
      case 'medium':
        return 'متوسط'
      case 'low':
        return 'منخفض'
      default:
        return priority
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <Card className="group cursor-pointer transition-all duration-300 hover-lift border-2 border-gray-200 hover:border-blue-300 bg-white hover:shadow-strong animate-fade-in">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 space-x-reverse mb-2">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                <Package className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                  #{trackingNumber}
                </h3>
                <p className="text-sm text-gray-500">طلب رقم {id}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2 space-x-reverse">
              <Badge className={`${getStatusColor(status)} border`}>
                {getStatusText(status)}
              </Badge>
              <Badge className={`${getPriorityColor(priority)} border-0`}>
                {getPriorityText(priority)}
              </Badge>
            </div>
          </div>
          
          <div className="text-left">
            <div className="text-2xl font-bold text-green-600">
              {amount.toLocaleString('ar-EG')} د.ع
            </div>
            <p className="text-xs text-gray-500">المبلغ</p>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-4">
          {/* معلومات العميل */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
              <User className="h-4 w-4" />
              <span className="font-medium">{customerName}</span>
            </div>
            
            <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
              <Phone className="h-4 w-4" />
              <span>{customerPhone}</span>
            </div>
            
            <div className="flex items-start space-x-2 space-x-reverse text-sm text-gray-600">
              <MapPin className="h-4 w-4 mt-0.5" />
              <span className="flex-1">{customerAddress}</span>
            </div>
          </div>

          {/* معلومات التوصيل */}
          <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-100">
            <div>
              <div className="flex items-center space-x-1 space-x-reverse mb-1">
                <Calendar className="h-4 w-4 text-gray-400" />
                <span className="text-xs text-gray-500">تاريخ الإنشاء</span>
              </div>
              <p className="text-sm font-medium text-gray-900">{formatDate(createdAt)}</p>
            </div>
            
            {deliveryDate && (
              <div>
                <div className="flex items-center space-x-1 space-x-reverse mb-1">
                  <Clock className="h-4 w-4 text-gray-400" />
                  <span className="text-xs text-gray-500">موعد التوصيل</span>
                </div>
                <p className="text-sm font-medium text-gray-900">{formatDate(deliveryDate)}</p>
              </div>
            )}
          </div>

          {/* المندوب المكلف */}
          {assignedTo && (
            <div className="flex items-center space-x-2 space-x-reverse pt-2 border-t border-gray-100">
              <Truck className="h-4 w-4 text-blue-500" />
              <span className="text-sm text-gray-600">المندوب:</span>
              <span className="text-sm font-medium text-blue-600">{assignedTo}</span>
            </div>
          )}

          {/* الملاحظات */}
          {notes && (
            <div className="pt-2 border-t border-gray-100">
              <p className="text-xs text-gray-500 mb-1">ملاحظات:</p>
              <p className="text-sm text-gray-700 bg-gray-50 p-2 rounded-lg">{notes}</p>
            </div>
          )}

          {/* أزرار الإجراءات */}
          <div className="flex items-center space-x-2 space-x-reverse pt-4 border-t border-gray-100">
            {onStatusChange && (
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => onStatusChange(id, status)}
                className="flex-1 text-xs"
              >
                تغيير الحالة
              </Button>
            )}
            
            {onAssign && !assignedTo && (
              <Button 
                size="sm" 
                onClick={() => onAssign(id)}
                className="flex-1 text-xs"
              >
                إسناد مندوب
              </Button>
            )}
            
            <Button 
              size="sm" 
              variant="ghost"
              className="text-xs text-blue-600 hover:text-blue-700"
            >
              عرض التفاصيل
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
