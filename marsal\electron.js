const { app, BrowserWindow, Menu, shell } = require('electron')
const path = require('path')
const isDev = process.env.NODE_ENV === 'development'

function createWindow() {
  // إنشاء نافذة المتصفح
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true
    },
    icon: path.join(__dirname, 'public/favicon.ico'),
    title: 'مرسال - نظام إدارة التوصيل',
    show: false, // لا تظهر النافذة حتى تكون جاهزة
    titleBarStyle: 'default'
  })

  // تحميل التطبيق
  if (isDev) {
    mainWindow.loadURL('http://localhost:3000')
    // فتح أدوات المطور في وضع التطوير
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(path.join(__dirname, 'out/index.html'))
  }

  // إظهار النافذة عندما تكون جاهزة
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()
    
    // التركيز على النافذة
    if (isDev) {
      mainWindow.focus()
    }
  })

  // التعامل مع الروابط الخارجية
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url)
    return { action: 'deny' }
  })

  // منع التنقل إلى مواقع خارجية
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl)
    
    if (parsedUrl.origin !== 'http://localhost:3000' && !navigationUrl.startsWith('file://')) {
      event.preventDefault()
      shell.openExternal(navigationUrl)
    }
  })

  return mainWindow
}

// إعداد القائمة
function createMenu() {
  const template = [
    {
      label: 'ملف',
      submenu: [
        {
          label: 'إعادة تحميل',
          accelerator: 'CmdOrCtrl+R',
          click: (item, focusedWindow) => {
            if (focusedWindow) focusedWindow.reload()
          }
        },
        {
          label: 'إعادة تحميل قسري',
          accelerator: 'CmdOrCtrl+Shift+R',
          click: (item, focusedWindow) => {
            if (focusedWindow) focusedWindow.webContents.reloadIgnoringCache()
          }
        },
        { type: 'separator' },
        {
          label: 'خروج',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: 'عرض',
      submenu: [
        {
          label: 'تكبير',
          accelerator: 'CmdOrCtrl+Plus',
          click: (item, focusedWindow) => {
            if (focusedWindow) {
              const currentZoom = focusedWindow.webContents.getZoomLevel()
              focusedWindow.webContents.setZoomLevel(currentZoom + 1)
            }
          }
        },
        {
          label: 'تصغير',
          accelerator: 'CmdOrCtrl+-',
          click: (item, focusedWindow) => {
            if (focusedWindow) {
              const currentZoom = focusedWindow.webContents.getZoomLevel()
              focusedWindow.webContents.setZoomLevel(currentZoom - 1)
            }
          }
        },
        {
          label: 'حجم طبيعي',
          accelerator: 'CmdOrCtrl+0',
          click: (item, focusedWindow) => {
            if (focusedWindow) focusedWindow.webContents.setZoomLevel(0)
          }
        },
        { type: 'separator' },
        {
          label: 'ملء الشاشة',
          accelerator: process.platform === 'darwin' ? 'Ctrl+Cmd+F' : 'F11',
          click: (item, focusedWindow) => {
            if (focusedWindow) {
              focusedWindow.setFullScreen(!focusedWindow.isFullScreen())
            }
          }
        }
      ]
    },
    {
      label: 'نافذة',
      submenu: [
        {
          label: 'تصغير',
          accelerator: 'CmdOrCtrl+M',
          click: (item, focusedWindow) => {
            if (focusedWindow) focusedWindow.minimize()
          }
        },
        {
          label: 'إغلاق',
          accelerator: 'CmdOrCtrl+W',
          click: (item, focusedWindow) => {
            if (focusedWindow) focusedWindow.close()
          }
        }
      ]
    }
  ]

  // إضافة قائمة المطور في وضع التطوير
  if (isDev) {
    template.push({
      label: 'مطور',
      submenu: [
        {
          label: 'أدوات المطور',
          accelerator: process.platform === 'darwin' ? 'Alt+Cmd+I' : 'Ctrl+Shift+I',
          click: (item, focusedWindow) => {
            if (focusedWindow) focusedWindow.webContents.toggleDevTools()
          }
        }
      ]
    })
  }

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

// هذا الأسلوب سيتم استدعاؤه عندما ينتهي Electron من التهيئة
app.whenReady().then(() => {
  createMenu()
  createWindow()

  app.on('activate', () => {
    // على macOS من الشائع إعادة إنشاء نافذة في التطبيق عندما يتم النقر على أيقونة dock
    // ولا توجد نوافذ أخرى مفتوحة
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// الخروج عندما يتم إغلاق جميع النوافذ، باستثناء macOS
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit()
})

// في هذا الملف يمكنك تضمين باقي كود العملية الرئيسية للتطبيق
// يمكنك أيضاً وضعها في ملفات منفصلة وتطلبها هنا
