import { Order, OrderStatus, OrderStatusUpdate, Statistics, Notification } from '@/types'
import { generateTrackingNumber } from './utils'

// مفاتيح التخزين المحلي
const STORAGE_KEYS = {
  ORDERS: 'marsal_orders',
  NOTIFICATIONS: 'marsal_notifications',
  SETTINGS: 'marsal_settings',
} as const

// الحصول على جميع الطلبات
export function getOrders(): Order[] {
  if (typeof window === 'undefined') return []
  
  const ordersData = localStorage.getItem(STORAGE_KEYS.ORDERS)
  if (!ordersData) return []
  
  try {
    return JSON.parse(ordersData)
  } catch {
    return []
  }
}

// إضافة طلب جديد
export function addOrder(orderData: Omit<Order, 'id' | 'trackingNumber' | 'statusHistory' | 'createdAt' | 'updatedAt'>): Order {
  const orders = getOrders()
  
  const newOrder: Order = {
    ...orderData,
    id: Date.now().toString(),
    trackingNumber: generateTrackingNumber(),
    statusHistory: [{
      id: Date.now().toString(),
      orderId: '',
      status: orderData.status,
      notes: 'تم إنشاء الطلب',
      updatedBy: 'النظام',
      updatedAt: new Date().toISOString(),
    }],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }
  
  // تحديث معرف الطلب في تاريخ الحالة
  newOrder.statusHistory[0].orderId = newOrder.id
  
  orders.push(newOrder)
  localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(orders))
  
  return newOrder
}

// تحديث طلب
export function updateOrder(orderId: string, updates: Partial<Order>): boolean {
  const orders = getOrders()
  const orderIndex = orders.findIndex(o => o.id === orderId)
  
  if (orderIndex === -1) return false
  
  orders[orderIndex] = {
    ...orders[orderIndex],
    ...updates,
    updatedAt: new Date().toISOString(),
  }
  
  localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(orders))
  return true
}

// تحديث حالة الطلب
export function updateOrderStatus(
  orderId: string,
  status: OrderStatus,
  notes?: string,
  image?: string,
  returnReason?: string,
  returnedPieces?: number,
  newAmount?: number,
  updatedBy: string = 'المستخدم'
): boolean {
  const orders = getOrders()
  const orderIndex = orders.findIndex(o => o.id === orderId)
  
  if (orderIndex === -1) return false
  
  const order = orders[orderIndex]
  
  // إنشاء تحديث جديد للحالة
  const statusUpdate: OrderStatusUpdate = {
    id: Date.now().toString(),
    orderId,
    status,
    notes,
    image,
    returnReason: returnReason as any,
    returnedPieces,
    newAmount,
    updatedBy,
    updatedAt: new Date().toISOString(),
  }
  
  // تحديث الطلب
  order.status = status
  order.statusHistory.push(statusUpdate)
  order.updatedAt = new Date().toISOString()
  
  // تحديث البيانات الإضافية حسب الحالة
  if (returnReason) order.returnReason = returnReason as any
  if (returnedPieces !== undefined) order.returnedPieces = returnedPieces
  if (newAmount !== undefined) {
    order.originalAmount = order.originalAmount || order.amount
    order.amount = newAmount
  }
  if (notes) order.notes = notes
  
  localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(orders))
  
  // إضافة إشعار
  addNotification({
    title: 'تحديث حالة الطلب',
    message: `تم تحديث حالة الطلب ${order.trackingNumber} إلى ${getStatusLabel(status)}`,
    type: 'info',
  })
  
  return true
}

// حذف طلب
export function deleteOrder(orderId: string): boolean {
  const orders = getOrders()
  const filteredOrders = orders.filter(o => o.id !== orderId)
  
  if (filteredOrders.length === orders.length) return false
  
  localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(filteredOrders))
  return true
}

// البحث في الطلبات
export function searchOrders(query: string): Order[] {
  const orders = getOrders()
  const searchTerm = query.toLowerCase()
  
  return orders.filter(order =>
    order.trackingNumber.toLowerCase().includes(searchTerm) ||
    order.senderName.toLowerCase().includes(searchTerm) ||
    order.recipientName.toLowerCase().includes(searchTerm) ||
    order.recipientPhone.includes(searchTerm) ||
    order.senderPhone.includes(searchTerm)
  )
}

// فلترة الطلبات حسب الحالة
export function filterOrdersByStatus(status: OrderStatus): Order[] {
  return getOrders().filter(order => order.status === status)
}

// فلترة الطلبات حسب المندوب
export function filterOrdersByCourier(courierId: string): Order[] {
  return getOrders().filter(order => order.courierId === courierId)
}

// إسناد طلبات لمندوب
export function assignOrdersToCourier(orderIds: string[], courierId: string, courierName: string, courierPhone: string): boolean {
  const orders = getOrders()
  let updated = false
  
  orders.forEach(order => {
    if (orderIds.includes(order.id)) {
      order.courierId = courierId
      order.courierName = courierName
      order.courierPhone = courierPhone
      order.status = 'processing'
      order.updatedAt = new Date().toISOString()
      
      // إضافة تحديث للحالة
      order.statusHistory.push({
        id: Date.now().toString() + Math.random(),
        orderId: order.id,
        status: 'processing',
        notes: `تم إسناد الطلب للمندوب ${courierName}`,
        updatedBy: 'النظام',
        updatedAt: new Date().toISOString(),
      })
      
      updated = true
    }
  })
  
  if (updated) {
    localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(orders))
  }
  
  return updated
}

// تخصيص الطلبات للمندوبين
export function assignOrderToCourier(orderId: string, courierId: string, courierName: string): boolean {
  const orders = getOrders()
  const orderIndex = orders.findIndex(order => order.id === orderId)

  if (orderIndex === -1) return false

  orders[orderIndex] = {
    ...orders[orderIndex],
    courierId,
    courierName,
    updatedAt: new Date().toISOString()
  }

  localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(orders))
  return true
}

// دوال إدارة الموظفين
export function getEmployees(): User[] {
  const users = getUsers()
  return users.filter(user => user.role !== 'manager')
}

export function addEmployee(employeeData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): User {
  const users = getUsers()
  const newEmployee: User = {
    ...employeeData,
    id: generateId(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }

  users.push(newEmployee)
  localStorage.setItem('users', JSON.stringify(users))
  return newEmployee
}

export function updateEmployee(employeeId: string, updates: Partial<User>): boolean {
  const users = getUsers()
  const index = users.findIndex(user => user.id === employeeId)

  if (index === -1) return false

  users[index] = {
    ...users[index],
    ...updates,
    updatedAt: new Date().toISOString()
  }

  localStorage.setItem('users', JSON.stringify(users))
  return true
}

export function deleteEmployee(employeeId: string): boolean {
  const users = getUsers()
  const filteredUsers = users.filter(user => user.id !== employeeId)

  if (filteredUsers.length === users.length) return false

  localStorage.setItem('users', JSON.stringify(filteredUsers))
  return true
}

export function searchEmployees(query: string): User[] {
  const employees = getEmployees()
  const searchTerm = query.toLowerCase()

  return employees.filter(employee =>
    employee.name.toLowerCase().includes(searchTerm) ||
    employee.username.toLowerCase().includes(searchTerm) ||
    (employee.phone && employee.phone.includes(searchTerm)) ||
    (employee.email && employee.email.toLowerCase().includes(searchTerm))
  )
}

// حساب الإحصائيات
export function calculateStatistics(courierId?: string): Statistics {
  const orders = courierId ? filterOrdersByCourier(courierId) : getOrders()
  
  const stats: Statistics = {
    totalOrders: orders.length,
    pendingOrders: orders.filter(o => o.status === 'pending').length,
    processingOrders: orders.filter(o => o.status === 'processing').length,
    shippedOrders: orders.filter(o => o.status === 'shipped').length,
    outForDeliveryOrders: orders.filter(o => o.status === 'out_for_delivery').length,
    deliveredOrders: orders.filter(o => o.status === 'delivered').length,
    returnedOrders: orders.filter(o => o.status === 'returned').length,
    cancelledOrders: orders.filter(o => o.status === 'cancelled').length,
    postponedOrders: orders.filter(o => o.status === 'postponed').length,
    totalRevenue: orders.filter(o => o.status === 'delivered').reduce((sum, o) => sum + o.amount, 0),
    todayOrders: 0,
    thisWeekOrders: 0,
    thisMonthOrders: 0,
  }
  
  // حساب الطلبات حسب التاريخ
  const today = new Date()
  const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()))
  const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
  
  orders.forEach(order => {
    const orderDate = new Date(order.createdAt)
    const todayDate = new Date()
    
    if (orderDate.toDateString() === todayDate.toDateString()) {
      stats.todayOrders++
    }
    
    if (orderDate >= startOfWeek) {
      stats.thisWeekOrders++
    }
    
    if (orderDate >= startOfMonth) {
      stats.thisMonthOrders++
    }
  })
  
  return stats
}

// إدارة الإشعارات
export function getNotifications(): Notification[] {
  if (typeof window === 'undefined') return []
  
  const notificationsData = localStorage.getItem(STORAGE_KEYS.NOTIFICATIONS)
  if (!notificationsData) return []
  
  try {
    return JSON.parse(notificationsData)
  } catch {
    return []
  }
}

export function addNotification(notification: Omit<Notification, 'id' | 'isRead' | 'createdAt'>): void {
  const notifications = getNotifications()
  
  const newNotification: Notification = {
    ...notification,
    id: Date.now().toString(),
    isRead: false,
    createdAt: new Date().toISOString(),
  }
  
  notifications.unshift(newNotification) // إضافة في المقدمة
  
  // الاحتفاظ بآخر 100 إشعار فقط
  if (notifications.length > 100) {
    notifications.splice(100)
  }
  
  localStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify(notifications))
}

export function markNotificationAsRead(notificationId: string): boolean {
  const notifications = getNotifications()
  const notification = notifications.find(n => n.id === notificationId)
  
  if (!notification) return false
  
  notification.isRead = true
  localStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify(notifications))
  return true
}

// دالة مساعدة للحصول على تسمية الحالة
export function getStatusLabel(status: OrderStatus): string {
  const statusLabels: Record<OrderStatus, string> = {
    pending: 'في الانتظار',
    processing: 'قيد المعالجة',
    shipped: 'تم الشحن',
    out_for_delivery: 'قيد التوصيل',
    delivered: 'تم التسليم',
    returned: 'راجع',
    cancelled: 'ملغي',
    postponed: 'مؤجل',
  }
  
  return statusLabels[status] || status
}
