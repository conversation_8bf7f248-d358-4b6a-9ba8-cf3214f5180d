'use client'

import React, { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useAuth } from '@/components/auth-provider'
import { Header } from '@/components/header'
import { Navigation } from '@/components/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { getOrders, updateOrderStatus, getStatusLabel } from '@/lib/storage'
import { Order, OrderStatus, ReturnReason } from '@/types'
import { ArrowRight, Save, Upload, Camera } from 'lucide-react'

export default function EditOrderPage() {
  const { user, isLoading } = useAuth()
  const router = useRouter()
  const params = useParams()
  const [order, setOrder] = useState<Order | null>(null)
  const [isLoadingOrder, setIsLoadingOrder] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const [formData, setFormData] = useState({
    status: 'pending' as OrderStatus,
    notes: '',
    image: '',
    returnReason: '' as ReturnReason | '',
    returnedPieces: '',
    newAmount: '',
  })

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/login')
      return
    }

    if (user && params.id) {
      loadOrder(params.id as string)
    }
  }, [user, isLoading, router, params.id])

  const loadOrder = (orderId: string) => {
    setIsLoadingOrder(true)
    try {
      const orders = getOrders()
      const foundOrder = orders.find(o => o.id === orderId)
      
      if (!foundOrder) {
        router.push('/orders')
        return
      }

      // التحقق من الصلاحيات
      if (user?.role === 'courier' && foundOrder.courierId !== user.id) {
        router.push('/orders')
        return
      }

      setOrder(foundOrder)
      setFormData({
        status: foundOrder.status,
        notes: '',
        image: '',
        returnReason: '',
        returnedPieces: '',
        newAmount: '',
      })
    } catch (error) {
      console.error('Error loading order:', error)
      router.push('/orders')
    } finally {
      setIsLoadingOrder(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // إزالة الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // التحقق من أسباب الإرجاع
    if (formData.status === 'returned' && !formData.returnReason) {
      newErrors.returnReason = 'سبب الإرجاع مطلوب'
    }

    // التحقق من القطع المرجعة
    if (formData.returnedPieces && formData.returnedPieces.trim()) {
      const pieces = parseInt(formData.returnedPieces)
      if (isNaN(pieces) || pieces < 0) {
        newErrors.returnedPieces = 'عدد القطع يجب أن يكون رقماً موجباً'
      }
    }

    // التحقق من المبلغ الجديد
    if (formData.newAmount && formData.newAmount.trim()) {
      const amount = parseFloat(formData.newAmount)
      if (isNaN(amount) || amount < 0) {
        newErrors.newAmount = 'المبلغ يجب أن يكون رقماً موجباً'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm() || !order) {
      return
    }

    setIsSubmitting(true)

    try {
      const success = updateOrderStatus(
        order.id,
        formData.status,
        formData.notes.trim() || undefined,
        formData.image.trim() || undefined,
        formData.returnReason || undefined,
        formData.returnedPieces ? parseInt(formData.returnedPieces) : undefined,
        formData.newAmount ? parseFloat(formData.newAmount) : undefined,
        user?.name || 'المستخدم'
      )

      if (success) {
        router.push(`/orders/${order.id}`)
      } else {
        setErrors({ submit: 'حدث خطأ أثناء تحديث الطلب' })
      }
    } catch (error) {
      console.error('Error updating order:', error)
      setErrors({ submit: 'حدث خطأ أثناء تحديث الطلب' })
    } finally {
      setIsSubmitting(false)
    }
  }

  const getStatusColor = (status: OrderStatus) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      processing: 'bg-blue-100 text-blue-800',
      shipped: 'bg-purple-100 text-purple-800',
      out_for_delivery: 'bg-orange-100 text-orange-800',
      delivered: 'bg-green-100 text-green-800',
      returned: 'bg-red-100 text-red-800',
      cancelled: 'bg-gray-100 text-gray-800',
      postponed: 'bg-indigo-100 text-indigo-800',
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
  }

  if (isLoading || isLoadingOrder) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!user || !order) return null

  const canEdit = user.role === 'manager' || user.role === 'supervisor' || 
                  (user.role === 'courier' && order.courierId === user.id)

  if (!canEdit) {
    router.push(`/orders/${order.id}`)
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="flex">
        <Navigation />
        
        <main className="flex-1 p-6">
          {/* العنوان */}
          <div className="flex items-center space-x-4 space-x-reverse mb-6">
            <Button
              variant="outline"
              onClick={() => router.back()}
              className="flex items-center space-x-2 space-x-reverse"
            >
              <ArrowRight className="h-4 w-4" />
              <span>رجوع</span>
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">تحديث حالة الطلب</h1>
              <p className="text-gray-600 mt-1">{order.trackingNumber}</p>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="max-w-4xl">
            <div className="grid gap-6">
              {/* معلومات الطلب الحالية */}
              <Card>
                <CardHeader>
                  <CardTitle>معلومات الطلب</CardTitle>
                  <CardDescription>البيانات الأساسية للطلب</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">المرسل</p>
                      <p className="font-semibold">{order.senderName}</p>
                      <p className="text-gray-500">{order.senderPhone}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">المستلم</p>
                      <p className="font-semibold">{order.recipientName}</p>
                      <p className="text-gray-500">{order.recipientPhone}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">المبلغ</p>
                      <p className="font-semibold">{order.amount.toLocaleString()} د.ع</p>
                      <div className={`inline-block px-2 py-1 rounded-full text-xs font-medium mt-1 ${getStatusColor(order.status)}`}>
                        {getStatusLabel(order.status)}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* تحديث الحالة */}
              <Card>
                <CardHeader>
                  <CardTitle>تحديث الحالة</CardTitle>
                  <CardDescription>تحديث حالة الطلب وإضافة ملاحظات</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="status">الحالة الجديدة *</Label>
                    <select
                      id="status"
                      value={formData.status}
                      onChange={(e) => handleInputChange('status', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="pending">في الانتظار</option>
                      <option value="processing">قيد المعالجة</option>
                      <option value="shipped">تم الشحن</option>
                      <option value="out_for_delivery">قيد التوصيل</option>
                      <option value="delivered">تم التسليم</option>
                      <option value="returned">راجع</option>
                      <option value="cancelled">ملغي</option>
                      <option value="postponed">مؤجل</option>
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="notes">ملاحظات التحديث</Label>
                    <textarea
                      id="notes"
                      value={formData.notes}
                      onChange={(e) => handleInputChange('notes', e.target.value)}
                      placeholder="أضف ملاحظات حول تحديث الحالة..."
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* تفاصيل الإرجاع */}
              {formData.status === 'returned' && (
                <Card>
                  <CardHeader>
                    <CardTitle>تفاصيل الإرجاع</CardTitle>
                    <CardDescription>معلومات إضافية عن سبب الإرجاع</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="returnReason">سبب الإرجاع *</Label>
                      <select
                        id="returnReason"
                        value={formData.returnReason}
                        onChange={(e) => handleInputChange('returnReason', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          errors.returnReason ? 'border-red-500' : 'border-gray-300'
                        }`}
                      >
                        <option value="">اختر سبب الإرجاع</option>
                        <option value="customer_refused">رفض العميل</option>
                        <option value="wrong_address">عنوان خاطئ</option>
                        <option value="customer_not_available">العميل غير متوفر</option>
                        <option value="damaged_item">البضاعة تالفة</option>
                        <option value="wrong_item">بضاعة خاطئة</option>
                        <option value="payment_issue">مشكلة في الدفع</option>
                        <option value="other">أخرى</option>
                      </select>
                      {errors.returnReason && (
                        <p className="text-sm text-red-600 mt-1">{errors.returnReason}</p>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="returnedPieces">عدد القطع المرجعة</Label>
                        <Input
                          id="returnedPieces"
                          type="number"
                          min="0"
                          value={formData.returnedPieces}
                          onChange={(e) => handleInputChange('returnedPieces', e.target.value)}
                          placeholder="0"
                          className={errors.returnedPieces ? 'border-red-500' : ''}
                        />
                        {errors.returnedPieces && (
                          <p className="text-sm text-red-600 mt-1">{errors.returnedPieces}</p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="newAmount">المبلغ الجديد (د.ع)</Label>
                        <Input
                          id="newAmount"
                          type="number"
                          min="0"
                          step="1000"
                          value={formData.newAmount}
                          onChange={(e) => handleInputChange('newAmount', e.target.value)}
                          placeholder={order.amount.toString()}
                          className={errors.newAmount ? 'border-red-500' : ''}
                        />
                        {errors.newAmount && (
                          <p className="text-sm text-red-600 mt-1">{errors.newAmount}</p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* رفع صورة */}
              <Card>
                <CardHeader>
                  <CardTitle>إرفاق صورة</CardTitle>
                  <CardDescription>رفع صورة توضيحية (اختياري)</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="image">رابط الصورة</Label>
                      <Input
                        id="image"
                        type="url"
                        value={formData.image}
                        onChange={(e) => handleInputChange('image', e.target.value)}
                        placeholder="https://example.com/image.jpg"
                      />
                    </div>
                    
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <Button
                        type="button"
                        variant="outline"
                        className="flex items-center space-x-2 space-x-reverse"
                      >
                        <Upload className="h-4 w-4" />
                        <span>رفع من الجهاز</span>
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        className="flex items-center space-x-2 space-x-reverse"
                      >
                        <Camera className="h-4 w-4" />
                        <span>التقاط صورة</span>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* أزرار الإجراءات */}
              <div className="flex justify-end space-x-4 space-x-reverse">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                  disabled={isSubmitting}
                >
                  إلغاء
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex items-center space-x-2 space-x-reverse"
                >
                  <Save className="h-4 w-4" />
                  <span>{isSubmitting ? 'جاري الحفظ...' : 'حفظ التحديث'}</span>
                </Button>
              </div>

              {errors.submit && (
                <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
                  {errors.submit}
                </div>
              )}
            </div>
          </form>
        </main>
      </div>
    </div>
  )
}
