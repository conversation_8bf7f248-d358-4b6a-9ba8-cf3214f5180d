{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/src/lib/auth.ts"], "sourcesContent": ["import { User, UserRole } from '@/types'\n\n// المستخدمون الافتراضيون\nconst defaultUsers: User[] = [\n  {\n    id: '1',\n    name: 'مدير النظام',\n    username: 'manager',\n    password: '123456',\n    phone: '07901234567',\n    role: 'manager',\n    isActive: true,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '2',\n    name: 'المتابع الأول',\n    username: 'supervisor',\n    password: '123456',\n    phone: '07901234568',\n    role: 'supervisor',\n    isActive: true,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '3',\n    name: 'المندوب الأول',\n    username: 'courier',\n    password: '123456',\n    phone: '07901234569',\n    role: 'courier',\n    isActive: true,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n]\n\n// تهيئة المستخدمين الافتراضيين\nexport function initializeDefaultUsers(): void {\n  if (typeof window === 'undefined') return\n  \n  const existingUsers = localStorage.getItem('marsal_users')\n  if (!existingUsers) {\n    localStorage.setItem('marsal_users', JSON.stringify(defaultUsers))\n  }\n}\n\n// تسجيل الدخول\nexport function login(username: string, password: string): User | null {\n  if (typeof window === 'undefined') return null\n  \n  const users = getUsers()\n  const user = users.find(u => u.username === username && u.password === password && u.isActive)\n  \n  if (user) {\n    // حفظ بيانات الجلسة\n    const session = {\n      userId: user.id,\n      username: user.username,\n      role: user.role,\n      loginTime: new Date().toISOString(),\n      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 ساعة\n    }\n    localStorage.setItem('marsal_session', JSON.stringify(session))\n    return user\n  }\n  \n  return null\n}\n\n// تسجيل الخروج\nexport function logout(): void {\n  if (typeof window === 'undefined') return\n  localStorage.removeItem('marsal_session')\n}\n\n// التحقق من الجلسة\nexport function getCurrentUser(): User | null {\n  if (typeof window === 'undefined') return null\n  \n  const sessionData = localStorage.getItem('marsal_session')\n  if (!sessionData) return null\n  \n  try {\n    const session = JSON.parse(sessionData)\n    const now = new Date()\n    const expiresAt = new Date(session.expiresAt)\n    \n    if (now > expiresAt) {\n      logout()\n      return null\n    }\n    \n    const users = getUsers()\n    const user = users.find(u => u.id === session.userId && u.isActive)\n    return user || null\n  } catch {\n    logout()\n    return null\n  }\n}\n\n// التحقق من صحة الجلسة\nexport function isAuthenticated(): boolean {\n  return getCurrentUser() !== null\n}\n\n// التحقق من الدور\nexport function hasRole(role: UserRole): boolean {\n  const user = getCurrentUser()\n  return user?.role === role\n}\n\n// التحقق من الصلاحية\nexport function hasPermission(permission: string): boolean {\n  const user = getCurrentUser()\n  if (!user) return false\n  \n  // المدير له جميع الصلاحيات\n  if (user.role === 'manager') return true\n  \n  // تحديد الصلاحيات حسب الدور\n  const permissions: Record<UserRole, string[]> = {\n    manager: ['*'], // جميع الصلاحيات\n    supervisor: [\n      'view_orders',\n      'create_orders',\n      'edit_orders',\n      'view_statistics',\n      'manage_users',\n      'view_archive',\n      'manage_notifications',\n      'manage_settings',\n      'return_delivered_orders', // صلاحية خاصة\n    ],\n    courier: [\n      'view_orders',\n      'create_orders',\n      'edit_orders',\n      'view_statistics',\n      'view_archive_own',\n      'manage_notifications',\n      'manage_settings',\n    ],\n  }\n  \n  const userPermissions = permissions[user.role] || []\n  return userPermissions.includes('*') || userPermissions.includes(permission)\n}\n\n// الحصول على جميع المستخدمين\nexport function getUsers(): User[] {\n  if (typeof window === 'undefined') return []\n  \n  const usersData = localStorage.getItem('marsal_users')\n  if (!usersData) {\n    initializeDefaultUsers()\n    return defaultUsers\n  }\n  \n  try {\n    return JSON.parse(usersData)\n  } catch {\n    return defaultUsers\n  }\n}\n\n// إضافة مستخدم جديد\nexport function addUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): User | null {\n  if (typeof window === 'undefined') return null\n  \n  const users = getUsers()\n  \n  // التحقق من تكرار اسم المستخدم\n  if (users.some(u => u.username === userData.username)) {\n    return null\n  }\n  \n  const newUser: User = {\n    ...userData,\n    id: Date.now().toString(),\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  }\n  \n  users.push(newUser)\n  localStorage.setItem('marsal_users', JSON.stringify(users))\n  \n  return newUser\n}\n\n// تحديث مستخدم\nexport function updateUser(userId: string, updates: Partial<User>): boolean {\n  if (typeof window === 'undefined') return false\n  \n  const users = getUsers()\n  const userIndex = users.findIndex(u => u.id === userId)\n  \n  if (userIndex === -1) return false\n  \n  users[userIndex] = {\n    ...users[userIndex],\n    ...updates,\n    updatedAt: new Date().toISOString(),\n  }\n  \n  localStorage.setItem('marsal_users', JSON.stringify(users))\n  return true\n}\n\n// حذف مستخدم\nexport function deleteUser(userId: string): boolean {\n  if (typeof window === 'undefined') return false\n  \n  const users = getUsers()\n  const filteredUsers = users.filter(u => u.id !== userId)\n  \n  if (filteredUsers.length === users.length) return false\n  \n  localStorage.setItem('marsal_users', JSON.stringify(filteredUsers))\n  return true\n}\n\n// الحصول على المندوبين فقط\nexport function getCouriers(): User[] {\n  return getUsers().filter(u => u.role === 'courier' && u.isActive)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA,yBAAyB;AACzB,MAAM,eAAuB;IAC3B;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,UAAU;QACV,OAAO;QACP,MAAM;QACN,UAAU;QACV,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;CACD;AAGM,SAAS;IACd,wCAAmC;;IAEnC,MAAM;AAIR;AAGO,SAAS,MAAM,QAAgB,EAAE,QAAgB;IACtD,wCAAmC,OAAO;;IAE1C,MAAM;IACN,MAAM;AAgBR;AAGO,SAAS;IACd,wCAAmC;;AAErC;AAGO,SAAS;IACd,wCAAmC,OAAO;;IAE1C,MAAM;AAoBR;AAGO,SAAS;IACd,OAAO,qBAAqB;AAC9B;AAGO,SAAS,QAAQ,IAAc;IACpC,MAAM,OAAO;IACb,OAAO,MAAM,SAAS;AACxB;AAGO,SAAS,cAAc,UAAkB;IAC9C,MAAM,OAAO;IACb,IAAI,CAAC,MAAM,OAAO;IAElB,2BAA2B;IAC3B,IAAI,KAAK,IAAI,KAAK,WAAW,OAAO;IAEpC,4BAA4B;IAC5B,MAAM,cAA0C;QAC9C,SAAS;YAAC;SAAI;QACd,YAAY;YACV;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;YACP;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,MAAM,kBAAkB,WAAW,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE;IACpD,OAAO,gBAAgB,QAAQ,CAAC,QAAQ,gBAAgB,QAAQ,CAAC;AACnE;AAGO,SAAS;IACd,wCAAmC,OAAO,EAAE;;IAE5C,MAAM;AAWR;AAGO,SAAS,QAAQ,QAAsD;IAC5E,wCAAmC,OAAO;;IAE1C,MAAM;IAON,MAAM;AAWR;AAGO,SAAS,WAAW,MAAc,EAAE,OAAsB;IAC/D,wCAAmC,OAAO;;IAE1C,MAAM;IACN,MAAM;AAYR;AAGO,SAAS,WAAW,MAAc;IACvC,wCAAmC,OAAO;;IAE1C,MAAM;IACN,MAAM;AAMR;AAGO,SAAS;IACd,OAAO,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,EAAE,QAAQ;AAClE", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/src/components/auth-provider.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\nimport { User } from '@/types'\nimport { getCurrentUser, initializeDefaultUsers } from '@/lib/auth'\n\ninterface AuthContextType {\n  user: User | null\n  isLoading: boolean\n  login: (user: User) => void\n  logout: () => void\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    // تهيئة المستخدمين الافتراضيين\n    initializeDefaultUsers()\n    \n    // التحقق من الجلسة الحالية\n    const currentUser = getCurrentUser()\n    setUser(currentUser)\n    setIsLoading(false)\n  }, [])\n\n  const login = (user: User) => {\n    setUser(user)\n  }\n\n  const logout = () => {\n    setUser(null)\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('marsal_session')\n    }\n  }\n\n  return (\n    <AuthContext.Provider value={{ user, isLoading, login, logout }}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AAaA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+BAA+B;QAC/B,CAAA,GAAA,kHAAA,CAAA,yBAAsB,AAAD;QAErB,2BAA2B;QAC3B,MAAM,cAAc,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;QACjC,QAAQ;QACR,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,QAAQ,CAAC;QACb,QAAQ;IACV;IAEA,MAAM,SAAS;QACb,QAAQ;QACR,uCAAmC;;QAEnC;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAM;YAAW;YAAO;QAAO;kBAC3D;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}