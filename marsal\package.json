{"name": "marsal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build", "cap:build": "npm run export && npx cap sync", "cap:android": "npm run cap:build && npx cap open android", "cap:ios": "npm run cap:build && npx cap open ios", "electron:build": "npm run export && electron-builder", "electron:dev": "npm run export && electron ."}, "dependencies": {"@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.460.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^2.5.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}, "main": "electron.js", "homepage": "./", "build": {"appId": "com.marsal.delivery", "productName": "مرسال", "directories": {"output": "dist"}, "files": ["out/**/*", "electron.js", "package.json"], "win": {"target": "nsis", "icon": "public/favicon.ico"}, "mac": {"target": "dmg", "icon": "public/favicon.ico"}, "linux": {"target": "AppImage", "icon": "public/favicon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}