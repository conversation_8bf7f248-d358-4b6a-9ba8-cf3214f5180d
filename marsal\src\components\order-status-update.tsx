'use client'

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { updateOrderStatus, getStatusLabel } from '@/lib/storage'
import { Order, OrderStatus, ReturnReason } from '@/types'
import { Save, X } from 'lucide-react'

interface OrderStatusUpdateProps {
  order: Order
  currentUser: string
  onUpdate: () => void
  onCancel: () => void
}

export function OrderStatusUpdate({ order, currentUser, onUpdate, onCancel }: OrderStatusUpdateProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const [formData, setFormData] = useState({
    status: order.status,
    notes: '',
    image: '',
    returnReason: '' as ReturnReason | '',
    returnedPieces: '',
    newAmount: '',
  })

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // إزالة الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // التحقق من أسباب الإرجاع
    if (formData.status === 'returned' && !formData.returnReason) {
      newErrors.returnReason = 'سبب الإرجاع مطلوب'
    }

    // التحقق من القطع المرجعة
    if (formData.returnedPieces && formData.returnedPieces.trim()) {
      const pieces = parseInt(formData.returnedPieces)
      if (isNaN(pieces) || pieces < 0) {
        newErrors.returnedPieces = 'عدد القطع يجب أن يكون رقماً موجباً'
      }
    }

    // التحقق من المبلغ الجديد
    if (formData.newAmount && formData.newAmount.trim()) {
      const amount = parseFloat(formData.newAmount)
      if (isNaN(amount) || amount < 0) {
        newErrors.newAmount = 'المبلغ يجب أن يكون رقماً موجباً'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      const success = updateOrderStatus(
        order.id,
        formData.status,
        formData.notes.trim() || undefined,
        formData.image.trim() || undefined,
        formData.returnReason || undefined,
        formData.returnedPieces ? parseInt(formData.returnedPieces) : undefined,
        formData.newAmount ? parseFloat(formData.newAmount) : undefined,
        currentUser
      )

      if (success) {
        onUpdate()
      } else {
        setErrors({ submit: 'حدث خطأ أثناء تحديث الطلب' })
      }
    } catch (error) {
      console.error('Error updating order:', error)
      setErrors({ submit: 'حدث خطأ أثناء تحديث الطلب' })
    } finally {
      setIsSubmitting(false)
    }
  }

  const getReturnReasonLabel = (reason: ReturnReason) => {
    const labels = {
      customer_refused: 'رفض العميل',
      wrong_address: 'عنوان خاطئ',
      customer_not_available: 'العميل غير متوفر',
      damaged_item: 'البضاعة تالفة',
      wrong_item: 'بضاعة خاطئة',
      payment_issue: 'مشكلة في الدفع',
      other: 'أخرى'
    }
    return labels[reason] || reason
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>تحديث حالة الطلب</CardTitle>
            <CardDescription>تحديث سريع لحالة الطلب {order.trackingNumber}</CardDescription>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancel}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* الحالة الجديدة */}
          <div>
            <Label htmlFor="status">الحالة الجديدة *</Label>
            <select
              id="status"
              value={formData.status}
              onChange={(e) => handleInputChange('status', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="pending">في الانتظار</option>
              <option value="processing">قيد المعالجة</option>
              <option value="shipped">تم الشحن</option>
              <option value="out_for_delivery">قيد التوصيل</option>
              <option value="delivered">تم التسليم</option>
              <option value="returned">راجع</option>
              <option value="cancelled">ملغي</option>
              <option value="postponed">مؤجل</option>
            </select>
          </div>

          {/* ملاحظات */}
          <div>
            <Label htmlFor="notes">ملاحظات</Label>
            <textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="أضف ملاحظات حول التحديث..."
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* تفاصيل الإرجاع */}
          {formData.status === 'returned' && (
            <div className="space-y-4 p-4 bg-red-50 rounded-md">
              <h4 className="font-medium text-red-900">تفاصيل الإرجاع</h4>
              
              <div>
                <Label htmlFor="returnReason">سبب الإرجاع *</Label>
                <select
                  id="returnReason"
                  value={formData.returnReason}
                  onChange={(e) => handleInputChange('returnReason', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.returnReason ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="">اختر سبب الإرجاع</option>
                  <option value="customer_refused">رفض العميل</option>
                  <option value="wrong_address">عنوان خاطئ</option>
                  <option value="customer_not_available">العميل غير متوفر</option>
                  <option value="damaged_item">البضاعة تالفة</option>
                  <option value="wrong_item">بضاعة خاطئة</option>
                  <option value="payment_issue">مشكلة في الدفع</option>
                  <option value="other">أخرى</option>
                </select>
                {errors.returnReason && (
                  <p className="text-sm text-red-600 mt-1">{errors.returnReason}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="returnedPieces">عدد القطع المرجعة</Label>
                  <Input
                    id="returnedPieces"
                    type="number"
                    min="0"
                    value={formData.returnedPieces}
                    onChange={(e) => handleInputChange('returnedPieces', e.target.value)}
                    placeholder="0"
                    className={errors.returnedPieces ? 'border-red-500' : ''}
                  />
                  {errors.returnedPieces && (
                    <p className="text-sm text-red-600 mt-1">{errors.returnedPieces}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="newAmount">المبلغ الجديد (د.ع)</Label>
                  <Input
                    id="newAmount"
                    type="number"
                    min="0"
                    step="1000"
                    value={formData.newAmount}
                    onChange={(e) => handleInputChange('newAmount', e.target.value)}
                    placeholder={order.amount.toString()}
                    className={errors.newAmount ? 'border-red-500' : ''}
                  />
                  {errors.newAmount && (
                    <p className="text-sm text-red-600 mt-1">{errors.newAmount}</p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* رابط الصورة */}
          <div>
            <Label htmlFor="image">رابط الصورة (اختياري)</Label>
            <Input
              id="image"
              type="url"
              value={formData.image}
              onChange={(e) => handleInputChange('image', e.target.value)}
              placeholder="https://example.com/image.jpg"
            />
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex justify-end space-x-4 space-x-reverse pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex items-center space-x-2 space-x-reverse"
            >
              <Save className="h-4 w-4" />
              <span>{isSubmitting ? 'جاري الحفظ...' : 'حفظ التحديث'}</span>
            </Button>
          </div>

          {errors.submit && (
            <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
              {errors.submit}
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  )
}
