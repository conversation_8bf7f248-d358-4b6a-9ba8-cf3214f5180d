{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// دوال مساعدة للتطبيق\nexport function formatDate(date: string | Date): string {\n  const d = new Date(date)\n  return d.toLocaleDateString('ar-IQ', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('ar-IQ', {\n    style: 'currency',\n    currency: 'IQD',\n    minimumFractionDigits: 0\n  }).format(amount)\n}\n\nexport function generateTrackingNumber(): string {\n  const prefix = 'MRS'\n  const timestamp = Date.now().toString().slice(-6)\n  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')\n  return `${prefix}${timestamp}${random}`\n}\n\nexport function validatePhoneNumber(phone: string): boolean {\n  // التحقق من أرقام الهواتف العراقية\n  const iraqiPhoneRegex = /^(\\+964|964|0)?(7[0-9]{9}|1[0-9]{9})$/\n  return iraqiPhoneRegex.test(phone.replace(/\\s/g, ''))\n}\n\nexport function validateIraqiPhone(phone: string): boolean {\n  return validatePhoneNumber(phone)\n}\n\nexport function copyToClipboard(text: string): Promise<void> {\n  return navigator.clipboard.writeText(text)\n}\n\nexport function shareViaWhatsApp(text: string): void {\n  const encodedText = encodeURIComponent(text)\n  const url = `https://wa.me/?text=${encodedText}`\n  window.open(url, '_blank')\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,QAAQ,KAAK;AACtB;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS;IACd,MAAM,SAAS;IACf,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC/C,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG;IACvE,OAAO,GAAG,SAAS,YAAY,QAAQ;AACzC;AAEO,SAAS,oBAAoB,KAAa;IAC/C,mCAAmC;IACnC,MAAM,kBAAkB;IACxB,OAAO,gBAAgB,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AACnD;AAEO,SAAS,mBAAmB,KAAa;IAC9C,OAAO,oBAAoB;AAC7B;AAEO,SAAS,gBAAgB,IAAY;IAC1C,OAAO,UAAU,SAAS,CAAC,SAAS,CAAC;AACvC;AAEO,SAAS,iBAAiB,IAAY;IAC3C,MAAM,cAAc,mBAAmB;IACvC,MAAM,MAAM,CAAC,oBAAoB,EAAE,aAAa;IAChD,OAAO,IAAI,CAAC,KAAK;AACnB", "debugId": null}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;;AAIA;;;;;;AAEA,MAAM,iBAAiB,IACrB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,OAAO;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/src/components/header.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { useAuth } from './auth-provider'\nimport { Button } from './ui/button'\nimport { LogOut, User, Bell, Search } from 'lucide-react'\nimport { Input } from './ui/input'\n\nexport function Header() {\n  const { user, logout } = useAuth()\n\n  const handleLogout = () => {\n    logout()\n    window.location.href = '/login'\n  }\n\n  return (\n    <header className=\"bg-white border-b border-gray-200 shadow-sm\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* الشعار */}\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <h1 className=\"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                مرسال\n              </h1>\n            </div>\n          </div>\n\n          {/* شريط البحث */}\n          <div className=\"flex-1 max-w-lg mx-8\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n                <Search className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <Input\n                type=\"text\"\n                placeholder=\"البحث برقم التتبع أو اسم العميل...\"\n                className=\"block w-full pr-10 pl-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n              />\n            </div>\n          </div>\n\n          {/* معلومات المستخدم والإجراءات */}\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            {/* الإشعارات */}\n            <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n              <Bell className=\"h-5 w-5\" />\n              <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white\"></span>\n            </Button>\n\n            {/* معلومات المستخدم */}\n            <div className=\"flex items-center space-x-3 space-x-reverse\">\n              <div className=\"flex items-center space-x-2 space-x-reverse\">\n                <User className=\"h-5 w-5 text-gray-400\" />\n                <div className=\"text-sm\">\n                  <div className=\"font-medium text-gray-900\">{user?.name}</div>\n                  <div className=\"text-gray-500\">\n                    {user?.role === 'manager' && 'مدير'}\n                    {user?.role === 'supervisor' && 'متابع'}\n                    {user?.role === 'courier' && 'مندوب'}\n                  </div>\n                </div>\n              </div>\n\n              {/* زر تسجيل الخروج */}\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={handleLogout}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <LogOut className=\"h-5 w-5\" />\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;;;;;;AAEA;AANA;;;;;;AAQO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAE/B,MAAM,eAAe;QACnB;QACA,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAgG;;;;;;;;;;;;;;;;kCAOlH,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAO,WAAU;;;;;;;;;;;8CAEpB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;;;;;;;;;;;;kCAMhB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAO,WAAU;;kDAC5C,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;;;;;;;;;;;;0CAIlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;;;;;;0DAChB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAA6B,MAAM;;;;;;kEAClD,8OAAC;wDAAI,WAAU;;4DACZ,MAAM,SAAS,aAAa;4DAC5B,MAAM,SAAS,gBAAgB;4DAC/B,MAAM,SAAS,aAAa;;;;;;;;;;;;;;;;;;;kDAMnC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC;4CAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlC", "debugId": null}}, {"offset": {"line": 416, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/src/components/navigation.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useAuth } from './auth-provider'\nimport { cn } from '@/lib/utils'\nimport {\n  Home,\n  Package,\n  Users,\n  BarChart3,\n  Archive,\n  Settings,\n  Bell,\n  Truck,\n  RotateCcw,\n  Calculator,\n  Download,\n} from 'lucide-react'\n\ninterface NavigationItem {\n  name: string\n  href: string\n  icon: React.ComponentType<{ className?: string }>\n  roles: string[]\n}\n\nconst navigationItems: NavigationItem[] = [\n  {\n    name: 'الرئيسية',\n    href: '/',\n    icon: Home,\n    roles: ['manager', 'supervisor', 'courier'],\n  },\n  {\n    name: 'إدارة الطلبات',\n    href: '/orders',\n    icon: Package,\n    roles: ['manager', 'supervisor', 'courier'],\n  },\n  {\n    name: 'إسناد الطلبات',\n    href: '/dispatch',\n    icon: Truck,\n    roles: ['manager'],\n  },\n  {\n    name: 'إدارة الرواجع',\n    href: '/returns',\n    icon: RotateCcw,\n    roles: ['manager'],\n  },\n  {\n    name: 'المحاسبة',\n    href: '/accounting',\n    icon: Calculator,\n    roles: ['manager'],\n  },\n  {\n    name: 'الإحصائيات',\n    href: '/statistics',\n    icon: BarChart3,\n    roles: ['manager', 'supervisor', 'courier'],\n  },\n  {\n    name: 'الأرشيف',\n    href: '/archive',\n    icon: Archive,\n    roles: ['manager', 'supervisor', 'courier'],\n  },\n  {\n    name: 'إدارة الموظفين',\n    href: '/users',\n    icon: Users,\n    roles: ['manager', 'supervisor'],\n  },\n  {\n    name: 'استيراد وتصدير',\n    href: '/import-export',\n    icon: Download,\n    roles: ['manager'],\n  },\n  {\n    name: 'الإشعارات',\n    href: '/notifications',\n    icon: Bell,\n    roles: ['manager', 'supervisor', 'courier'],\n  },\n  {\n    name: 'الإعدادات',\n    href: '/settings',\n    icon: Settings,\n    roles: ['manager', 'supervisor', 'courier'],\n  },\n]\n\nexport function Navigation() {\n  const { user } = useAuth()\n  const pathname = usePathname()\n\n  if (!user) return null\n\n  // فلترة العناصر حسب دور المستخدم\n  const filteredItems = navigationItems.filter(item =>\n    item.roles.includes(user.role)\n  )\n\n  return (\n    <nav className=\"bg-white border-r border-gray-200 w-64 min-h-screen\">\n      <div className=\"p-4\">\n        <ul className=\"space-y-2\">\n          {filteredItems.map((item) => {\n            const Icon = item.icon\n            const isActive = pathname === item.href\n\n            return (\n              <li key={item.name}>\n                <Link\n                  href={item.href}\n                  className={cn(\n                    'flex items-center space-x-3 space-x-reverse px-3 py-2 rounded-md text-sm font-medium transition-colors',\n                    isActive\n                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'\n                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                  )}\n                >\n                  <Icon className=\"h-5 w-5\" />\n                  <span>{item.name}</span>\n                </Link>\n              </li>\n            )\n          })}\n        </ul>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;;;;;;AANA;;;;;;;AA4BA,MAAM,kBAAoC;IACxC;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;YAAW;YAAc;SAAU;IAC7C;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;YAAW;YAAc;SAAU;IAC7C;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;SAAU;IACpB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;SAAU;IACpB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;SAAU;IACpB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;YAAW;YAAc;SAAU;IAC7C;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;YAAW;YAAc;SAAU;IAC7C;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;YAAW;SAAa;IAClC;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;SAAU;IACpB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;YAAW;YAAc;SAAU;IAC7C;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;QACN,OAAO;YAAC;YAAW;YAAc;SAAU;IAC7C;CACD;AAEM,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,IAAI,CAAC,MAAM,OAAO;IAElB,iCAAiC;IACjC,MAAM,gBAAgB,gBAAgB,MAAM,CAAC,CAAA,OAC3C,KAAK,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI;IAG/B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAG,WAAU;0BACX,cAAc,GAAG,CAAC,CAAC;oBAClB,MAAM,OAAO,KAAK,IAAI;oBACtB,MAAM,WAAW,aAAa,KAAK,IAAI;oBAEvC,qBACE,8OAAC;kCACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM,KAAK,IAAI;4BACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA,WACI,wDACA;;8CAGN,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;8CAAM,KAAK,IAAI;;;;;;;;;;;;uBAXX,KAAK,IAAI;;;;;gBAetB;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 688, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/src/lib/storage.ts"], "sourcesContent": ["import { Order, OrderStatus, OrderStatusUpdate, Statistics, Notification } from '@/types'\nimport { generateTrackingNumber } from './utils'\n\n// مفاتيح التخزين المحلي\nconst STORAGE_KEYS = {\n  ORDERS: 'marsal_orders',\n  NOTIFICATIONS: 'marsal_notifications',\n  SETTINGS: 'marsal_settings',\n} as const\n\n// الحصول على جميع الطلبات\nexport function getOrders(): Order[] {\n  if (typeof window === 'undefined') return []\n  \n  const ordersData = localStorage.getItem(STORAGE_KEYS.ORDERS)\n  if (!ordersData) return []\n  \n  try {\n    return JSON.parse(ordersData)\n  } catch {\n    return []\n  }\n}\n\n// إضافة طلب جديد\nexport function addOrder(orderData: Omit<Order, 'id' | 'trackingNumber' | 'statusHistory' | 'createdAt' | 'updatedAt'>): Order {\n  const orders = getOrders()\n  \n  const newOrder: Order = {\n    ...orderData,\n    id: Date.now().toString(),\n    trackingNumber: generateTrackingNumber(),\n    statusHistory: [{\n      id: Date.now().toString(),\n      orderId: '',\n      status: orderData.status,\n      notes: 'تم إنشاء الطلب',\n      updatedBy: 'النظام',\n      updatedAt: new Date().toISOString(),\n    }],\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  }\n  \n  // تحديث معرف الطلب في تاريخ الحالة\n  newOrder.statusHistory[0].orderId = newOrder.id\n  \n  orders.push(newOrder)\n  localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(orders))\n  \n  return newOrder\n}\n\n// تحديث طلب\nexport function updateOrder(orderId: string, updates: Partial<Order>): boolean {\n  const orders = getOrders()\n  const orderIndex = orders.findIndex(o => o.id === orderId)\n  \n  if (orderIndex === -1) return false\n  \n  orders[orderIndex] = {\n    ...orders[orderIndex],\n    ...updates,\n    updatedAt: new Date().toISOString(),\n  }\n  \n  localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(orders))\n  return true\n}\n\n// تحديث حالة الطلب\nexport function updateOrderStatus(\n  orderId: string,\n  status: OrderStatus,\n  notes?: string,\n  image?: string,\n  returnReason?: string,\n  returnedPieces?: number,\n  newAmount?: number,\n  updatedBy: string = 'المستخدم'\n): boolean {\n  const orders = getOrders()\n  const orderIndex = orders.findIndex(o => o.id === orderId)\n  \n  if (orderIndex === -1) return false\n  \n  const order = orders[orderIndex]\n  \n  // إنشاء تحديث جديد للحالة\n  const statusUpdate: OrderStatusUpdate = {\n    id: Date.now().toString(),\n    orderId,\n    status,\n    notes,\n    image,\n    returnReason: returnReason as any,\n    returnedPieces,\n    newAmount,\n    updatedBy,\n    updatedAt: new Date().toISOString(),\n  }\n  \n  // تحديث الطلب\n  order.status = status\n  order.statusHistory.push(statusUpdate)\n  order.updatedAt = new Date().toISOString()\n  \n  // تحديث البيانات الإضافية حسب الحالة\n  if (returnReason) order.returnReason = returnReason as any\n  if (returnedPieces !== undefined) order.returnedPieces = returnedPieces\n  if (newAmount !== undefined) {\n    order.originalAmount = order.originalAmount || order.amount\n    order.amount = newAmount\n  }\n  if (notes) order.notes = notes\n  \n  localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(orders))\n  \n  // إضافة إشعار\n  addNotification({\n    title: 'تحديث حالة الطلب',\n    message: `تم تحديث حالة الطلب ${order.trackingNumber} إلى ${getStatusLabel(status)}`,\n    type: 'info',\n  })\n  \n  return true\n}\n\n// حذف طلب\nexport function deleteOrder(orderId: string): boolean {\n  const orders = getOrders()\n  const filteredOrders = orders.filter(o => o.id !== orderId)\n  \n  if (filteredOrders.length === orders.length) return false\n  \n  localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(filteredOrders))\n  return true\n}\n\n// البحث في الطلبات\nexport function searchOrders(query: string): Order[] {\n  const orders = getOrders()\n  const searchTerm = query.toLowerCase()\n  \n  return orders.filter(order =>\n    order.trackingNumber.toLowerCase().includes(searchTerm) ||\n    order.senderName.toLowerCase().includes(searchTerm) ||\n    order.recipientName.toLowerCase().includes(searchTerm) ||\n    order.recipientPhone.includes(searchTerm) ||\n    order.senderPhone.includes(searchTerm)\n  )\n}\n\n// فلترة الطلبات حسب الحالة\nexport function filterOrdersByStatus(status: OrderStatus): Order[] {\n  return getOrders().filter(order => order.status === status)\n}\n\n// فلترة الطلبات حسب المندوب\nexport function filterOrdersByCourier(courierId: string): Order[] {\n  return getOrders().filter(order => order.courierId === courierId)\n}\n\n// إسناد طلبات لمندوب\nexport function assignOrdersToCourier(orderIds: string[], courierId: string, courierName: string, courierPhone: string): boolean {\n  const orders = getOrders()\n  let updated = false\n  \n  orders.forEach(order => {\n    if (orderIds.includes(order.id)) {\n      order.courierId = courierId\n      order.courierName = courierName\n      order.courierPhone = courierPhone\n      order.status = 'processing'\n      order.updatedAt = new Date().toISOString()\n      \n      // إضافة تحديث للحالة\n      order.statusHistory.push({\n        id: Date.now().toString() + Math.random(),\n        orderId: order.id,\n        status: 'processing',\n        notes: `تم إسناد الطلب للمندوب ${courierName}`,\n        updatedBy: 'النظام',\n        updatedAt: new Date().toISOString(),\n      })\n      \n      updated = true\n    }\n  })\n  \n  if (updated) {\n    localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(orders))\n  }\n  \n  return updated\n}\n\n// تخصيص الطلبات للمندوبين\nexport function assignOrderToCourier(orderId: string, courierId: string, courierName: string): boolean {\n  const orders = getOrders()\n  const orderIndex = orders.findIndex(order => order.id === orderId)\n\n  if (orderIndex === -1) return false\n\n  orders[orderIndex] = {\n    ...orders[orderIndex],\n    courierId,\n    courierName,\n    updatedAt: new Date().toISOString()\n  }\n\n  localStorage.setItem(STORAGE_KEYS.ORDERS, JSON.stringify(orders))\n  return true\n}\n\n// دوال إدارة الموظفين\nexport function getEmployees(): User[] {\n  const users = getUsers()\n  return users.filter(user => user.role !== 'manager')\n}\n\nexport function addEmployee(employeeData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): User {\n  const users = getUsers()\n  const newEmployee: User = {\n    ...employeeData,\n    id: generateId(),\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString()\n  }\n\n  users.push(newEmployee)\n  localStorage.setItem('users', JSON.stringify(users))\n  return newEmployee\n}\n\nexport function updateEmployee(employeeId: string, updates: Partial<User>): boolean {\n  const users = getUsers()\n  const index = users.findIndex(user => user.id === employeeId)\n\n  if (index === -1) return false\n\n  users[index] = {\n    ...users[index],\n    ...updates,\n    updatedAt: new Date().toISOString()\n  }\n\n  localStorage.setItem('users', JSON.stringify(users))\n  return true\n}\n\nexport function deleteEmployee(employeeId: string): boolean {\n  const users = getUsers()\n  const filteredUsers = users.filter(user => user.id !== employeeId)\n\n  if (filteredUsers.length === users.length) return false\n\n  localStorage.setItem('users', JSON.stringify(filteredUsers))\n  return true\n}\n\nexport function searchEmployees(query: string): User[] {\n  const employees = getEmployees()\n  const searchTerm = query.toLowerCase()\n\n  return employees.filter(employee =>\n    employee.name.toLowerCase().includes(searchTerm) ||\n    employee.username.toLowerCase().includes(searchTerm) ||\n    (employee.phone && employee.phone.includes(searchTerm)) ||\n    (employee.email && employee.email.toLowerCase().includes(searchTerm))\n  )\n}\n\n// حساب الإحصائيات\nexport function calculateStatistics(courierId?: string): Statistics {\n  const orders = courierId ? filterOrdersByCourier(courierId) : getOrders()\n  \n  const stats: Statistics = {\n    totalOrders: orders.length,\n    pendingOrders: orders.filter(o => o.status === 'pending').length,\n    processingOrders: orders.filter(o => o.status === 'processing').length,\n    shippedOrders: orders.filter(o => o.status === 'shipped').length,\n    outForDeliveryOrders: orders.filter(o => o.status === 'out_for_delivery').length,\n    deliveredOrders: orders.filter(o => o.status === 'delivered').length,\n    returnedOrders: orders.filter(o => o.status === 'returned').length,\n    cancelledOrders: orders.filter(o => o.status === 'cancelled').length,\n    postponedOrders: orders.filter(o => o.status === 'postponed').length,\n    totalRevenue: orders.filter(o => o.status === 'delivered').reduce((sum, o) => sum + o.amount, 0),\n    todayOrders: 0,\n    thisWeekOrders: 0,\n    thisMonthOrders: 0,\n  }\n  \n  // حساب الطلبات حسب التاريخ\n  const today = new Date()\n  const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()))\n  const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)\n  \n  orders.forEach(order => {\n    const orderDate = new Date(order.createdAt)\n    const todayDate = new Date()\n    \n    if (orderDate.toDateString() === todayDate.toDateString()) {\n      stats.todayOrders++\n    }\n    \n    if (orderDate >= startOfWeek) {\n      stats.thisWeekOrders++\n    }\n    \n    if (orderDate >= startOfMonth) {\n      stats.thisMonthOrders++\n    }\n  })\n  \n  return stats\n}\n\n// إدارة الإشعارات\nexport function getNotifications(): Notification[] {\n  if (typeof window === 'undefined') return []\n  \n  const notificationsData = localStorage.getItem(STORAGE_KEYS.NOTIFICATIONS)\n  if (!notificationsData) return []\n  \n  try {\n    return JSON.parse(notificationsData)\n  } catch {\n    return []\n  }\n}\n\nexport function addNotification(notification: Omit<Notification, 'id' | 'isRead' | 'createdAt'>): void {\n  const notifications = getNotifications()\n  \n  const newNotification: Notification = {\n    ...notification,\n    id: Date.now().toString(),\n    isRead: false,\n    createdAt: new Date().toISOString(),\n  }\n  \n  notifications.unshift(newNotification) // إضافة في المقدمة\n  \n  // الاحتفاظ بآخر 100 إشعار فقط\n  if (notifications.length > 100) {\n    notifications.splice(100)\n  }\n  \n  localStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify(notifications))\n}\n\nexport function markNotificationAsRead(notificationId: string): boolean {\n  const notifications = getNotifications()\n  const notification = notifications.find(n => n.id === notificationId)\n  \n  if (!notification) return false\n  \n  notification.isRead = true\n  localStorage.setItem(STORAGE_KEYS.NOTIFICATIONS, JSON.stringify(notifications))\n  return true\n}\n\n// دالة مساعدة للحصول على تسمية الحالة\nexport function getStatusLabel(status: OrderStatus): string {\n  const statusLabels: Record<OrderStatus, string> = {\n    pending: 'في الانتظار',\n    processing: 'قيد المعالجة',\n    shipped: 'تم الشحن',\n    out_for_delivery: 'قيد التوصيل',\n    delivered: 'تم التسليم',\n    returned: 'راجع',\n    cancelled: 'ملغي',\n    postponed: 'مؤجل',\n  }\n  \n  return statusLabels[status] || status\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AACA;;AAEA,wBAAwB;AACxB,MAAM,eAAe;IACnB,QAAQ;IACR,eAAe;IACf,UAAU;AACZ;AAGO,SAAS;IACd,wCAAmC,OAAO,EAAE;;IAE5C,MAAM;AAQR;AAGO,SAAS,SAAS,SAA6F;IACpH,MAAM,SAAS;IAEf,MAAM,WAAkB;QACtB,GAAG,SAAS;QACZ,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,gBAAgB,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD;QACrC,eAAe;YAAC;gBACd,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,SAAS;gBACT,QAAQ,UAAU,MAAM;gBACxB,OAAO;gBACP,WAAW;gBACX,WAAW,IAAI,OAAO,WAAW;YACnC;SAAE;QACF,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,mCAAmC;IACnC,SAAS,aAAa,CAAC,EAAE,CAAC,OAAO,GAAG,SAAS,EAAE;IAE/C,OAAO,IAAI,CAAC;IACZ,aAAa,OAAO,CAAC,aAAa,MAAM,EAAE,KAAK,SAAS,CAAC;IAEzD,OAAO;AACT;AAGO,SAAS,YAAY,OAAe,EAAE,OAAuB;IAClE,MAAM,SAAS;IACf,MAAM,aAAa,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAElD,IAAI,eAAe,CAAC,GAAG,OAAO;IAE9B,MAAM,CAAC,WAAW,GAAG;QACnB,GAAG,MAAM,CAAC,WAAW;QACrB,GAAG,OAAO;QACV,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,aAAa,OAAO,CAAC,aAAa,MAAM,EAAE,KAAK,SAAS,CAAC;IACzD,OAAO;AACT;AAGO,SAAS,kBACd,OAAe,EACf,MAAmB,EACnB,KAAc,EACd,KAAc,EACd,YAAqB,EACrB,cAAuB,EACvB,SAAkB,EAClB,YAAoB,UAAU;IAE9B,MAAM,SAAS;IACf,MAAM,aAAa,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAElD,IAAI,eAAe,CAAC,GAAG,OAAO;IAE9B,MAAM,QAAQ,MAAM,CAAC,WAAW;IAEhC,0BAA0B;IAC1B,MAAM,eAAkC;QACtC,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB;QACA;QACA;QACA;QACA,cAAc;QACd;QACA;QACA;QACA,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,cAAc;IACd,MAAM,MAAM,GAAG;IACf,MAAM,aAAa,CAAC,IAAI,CAAC;IACzB,MAAM,SAAS,GAAG,IAAI,OAAO,WAAW;IAExC,qCAAqC;IACrC,IAAI,cAAc,MAAM,YAAY,GAAG;IACvC,IAAI,mBAAmB,WAAW,MAAM,cAAc,GAAG;IACzD,IAAI,cAAc,WAAW;QAC3B,MAAM,cAAc,GAAG,MAAM,cAAc,IAAI,MAAM,MAAM;QAC3D,MAAM,MAAM,GAAG;IACjB;IACA,IAAI,OAAO,MAAM,KAAK,GAAG;IAEzB,aAAa,OAAO,CAAC,aAAa,MAAM,EAAE,KAAK,SAAS,CAAC;IAEzD,cAAc;IACd,gBAAgB;QACd,OAAO;QACP,SAAS,CAAC,oBAAoB,EAAE,MAAM,cAAc,CAAC,KAAK,EAAE,eAAe,SAAS;QACpF,MAAM;IACR;IAEA,OAAO;AACT;AAGO,SAAS,YAAY,OAAe;IACzC,MAAM,SAAS;IACf,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAEnD,IAAI,eAAe,MAAM,KAAK,OAAO,MAAM,EAAE,OAAO;IAEpD,aAAa,OAAO,CAAC,aAAa,MAAM,EAAE,KAAK,SAAS,CAAC;IACzD,OAAO;AACT;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,SAAS;IACf,MAAM,aAAa,MAAM,WAAW;IAEpC,OAAO,OAAO,MAAM,CAAC,CAAA,QACnB,MAAM,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC,eAC5C,MAAM,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,eACxC,MAAM,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,eAC3C,MAAM,cAAc,CAAC,QAAQ,CAAC,eAC9B,MAAM,WAAW,CAAC,QAAQ,CAAC;AAE/B;AAGO,SAAS,qBAAqB,MAAmB;IACtD,OAAO,YAAY,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;AACtD;AAGO,SAAS,sBAAsB,SAAiB;IACrD,OAAO,YAAY,MAAM,CAAC,CAAA,QAAS,MAAM,SAAS,KAAK;AACzD;AAGO,SAAS,sBAAsB,QAAkB,EAAE,SAAiB,EAAE,WAAmB,EAAE,YAAoB;IACpH,MAAM,SAAS;IACf,IAAI,UAAU;IAEd,OAAO,OAAO,CAAC,CAAA;QACb,IAAI,SAAS,QAAQ,CAAC,MAAM,EAAE,GAAG;YAC/B,MAAM,SAAS,GAAG;YAClB,MAAM,WAAW,GAAG;YACpB,MAAM,YAAY,GAAG;YACrB,MAAM,MAAM,GAAG;YACf,MAAM,SAAS,GAAG,IAAI,OAAO,WAAW;YAExC,qBAAqB;YACrB,MAAM,aAAa,CAAC,IAAI,CAAC;gBACvB,IAAI,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM;gBACvC,SAAS,MAAM,EAAE;gBACjB,QAAQ;gBACR,OAAO,CAAC,uBAAuB,EAAE,aAAa;gBAC9C,WAAW;gBACX,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,UAAU;QACZ;IACF;IAEA,IAAI,SAAS;QACX,aAAa,OAAO,CAAC,aAAa,MAAM,EAAE,KAAK,SAAS,CAAC;IAC3D;IAEA,OAAO;AACT;AAGO,SAAS,qBAAqB,OAAe,EAAE,SAAiB,EAAE,WAAmB;IAC1F,MAAM,SAAS;IACf,MAAM,aAAa,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAE1D,IAAI,eAAe,CAAC,GAAG,OAAO;IAE9B,MAAM,CAAC,WAAW,GAAG;QACnB,GAAG,MAAM,CAAC,WAAW;QACrB;QACA;QACA,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,aAAa,OAAO,CAAC,aAAa,MAAM,EAAE,KAAK,SAAS,CAAC;IACzD,OAAO;AACT;AAGO,SAAS;IACd,MAAM,QAAQ;IACd,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;AAC5C;AAEO,SAAS,YAAY,YAA0D;IACpF,MAAM,QAAQ;IACd,MAAM,cAAoB;QACxB,GAAG,YAAY;QACf,IAAI;QACJ,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,MAAM,IAAI,CAAC;IACX,aAAa,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC;IAC7C,OAAO;AACT;AAEO,SAAS,eAAe,UAAkB,EAAE,OAAsB;IACvE,MAAM,QAAQ;IACd,MAAM,QAAQ,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAElD,IAAI,UAAU,CAAC,GAAG,OAAO;IAEzB,KAAK,CAAC,MAAM,GAAG;QACb,GAAG,KAAK,CAAC,MAAM;QACf,GAAG,OAAO;QACV,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,aAAa,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC;IAC7C,OAAO;AACT;AAEO,SAAS,eAAe,UAAkB;IAC/C,MAAM,QAAQ;IACd,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAEvD,IAAI,cAAc,MAAM,KAAK,MAAM,MAAM,EAAE,OAAO;IAElD,aAAa,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC;IAC7C,OAAO;AACT;AAEO,SAAS,gBAAgB,KAAa;IAC3C,MAAM,YAAY;IAClB,MAAM,aAAa,MAAM,WAAW;IAEpC,OAAO,UAAU,MAAM,CAAC,CAAA,WACtB,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,eACrC,SAAS,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,eACxC,SAAS,KAAK,IAAI,SAAS,KAAK,CAAC,QAAQ,CAAC,eAC1C,SAAS,KAAK,IAAI,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;AAE7D;AAGO,SAAS,oBAAoB,SAAkB;IACpD,MAAM,SAAS,YAAY,sBAAsB,aAAa;IAE9D,MAAM,QAAoB;QACxB,aAAa,OAAO,MAAM;QAC1B,eAAe,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;QAChE,kBAAkB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,cAAc,MAAM;QACtE,eAAe,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;QAChE,sBAAsB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,oBAAoB,MAAM;QAChF,iBAAiB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;QACpE,gBAAgB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;QAClE,iBAAiB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;QACpE,iBAAiB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;QACpE,cAAc,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QAC9F,aAAa;QACb,gBAAgB;QAChB,iBAAiB;IACnB;IAEA,2BAA2B;IAC3B,MAAM,QAAQ,IAAI;IAClB,MAAM,cAAc,IAAI,KAAK,MAAM,OAAO,CAAC,MAAM,OAAO,KAAK,MAAM,MAAM;IACzE,MAAM,eAAe,IAAI,KAAK,MAAM,WAAW,IAAI,MAAM,QAAQ,IAAI;IAErE,OAAO,OAAO,CAAC,CAAA;QACb,MAAM,YAAY,IAAI,KAAK,MAAM,SAAS;QAC1C,MAAM,YAAY,IAAI;QAEtB,IAAI,UAAU,YAAY,OAAO,UAAU,YAAY,IAAI;YACzD,MAAM,WAAW;QACnB;QAEA,IAAI,aAAa,aAAa;YAC5B,MAAM,cAAc;QACtB;QAEA,IAAI,aAAa,cAAc;YAC7B,MAAM,eAAe;QACvB;IACF;IAEA,OAAO;AACT;AAGO,SAAS;IACd,wCAAmC,OAAO,EAAE;;IAE5C,MAAM;AAQR;AAEO,SAAS,gBAAgB,YAA+D;IAC7F,MAAM,gBAAgB;IAEtB,MAAM,kBAAgC;QACpC,GAAG,YAAY;QACf,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,QAAQ;QACR,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,cAAc,OAAO,CAAC,iBAAiB,mBAAmB;;IAE1D,8BAA8B;IAC9B,IAAI,cAAc,MAAM,GAAG,KAAK;QAC9B,cAAc,MAAM,CAAC;IACvB;IAEA,aAAa,OAAO,CAAC,aAAa,aAAa,EAAE,KAAK,SAAS,CAAC;AAClE;AAEO,SAAS,uBAAuB,cAAsB;IAC3D,MAAM,gBAAgB;IACtB,MAAM,eAAe,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAEtD,IAAI,CAAC,cAAc,OAAO;IAE1B,aAAa,MAAM,GAAG;IACtB,aAAa,OAAO,CAAC,aAAa,aAAa,EAAE,KAAK,SAAS,CAAC;IAChE,OAAO;AACT;AAGO,SAAS,eAAe,MAAmB;IAChD,MAAM,eAA4C;QAChD,SAAS;QACT,YAAY;QACZ,SAAS;QACT,kBAAkB;QAClB,WAAW;QACX,UAAU;QACV,WAAW;QACX,WAAW;IACb;IAEA,OAAO,YAAY,CAAC,OAAO,IAAI;AACjC", "debugId": null}}, {"offset": {"line": 981, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/%D9%85%D9%86%D8%AF%D9%88%D8%A8/marsal/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/components/auth-provider'\nimport { Header } from '@/components/header'\nimport { Navigation } from '@/components/navigation'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { calculateStatistics } from '@/lib/storage'\nimport {\n  Package,\n  Users,\n  BarChart3,\n  Archive,\n  Settings,\n  Bell,\n  Truck,\n  RotateCcw,\n  Calculator,\n  Download,\n} from 'lucide-react'\n\ninterface DashboardCard {\n  title: string\n  description: string\n  icon: React.ComponentType<{ className?: string }>\n  href: string\n  gradient: string\n  roles: string[]\n}\n\nconst dashboardCards: DashboardCard[] = [\n  {\n    title: 'إدارة الطلبات',\n    description: 'إضافة وتعديل ومتابعة الطلبات',\n    icon: Package,\n    href: '/orders',\n    gradient: 'from-blue-500 to-blue-600',\n    roles: ['manager', 'supervisor', 'courier'],\n  },\n  {\n    title: 'إسناد الطلبات',\n    description: 'إسناد الطلبات للمندوبين',\n    icon: Truck,\n    href: '/dispatch',\n    gradient: 'from-green-500 to-green-600',\n    roles: ['manager'],\n  },\n  {\n    title: 'إدارة الرواجع',\n    description: 'متابعة الطلبات المرجعة',\n    icon: RotateCcw,\n    href: '/returns',\n    gradient: 'from-orange-500 to-orange-600',\n    roles: ['manager'],\n  },\n  {\n    title: 'المحاسبة',\n    description: 'التقارير المالية والحسابات',\n    icon: Calculator,\n    href: '/accounting',\n    gradient: 'from-purple-500 to-purple-600',\n    roles: ['manager'],\n  },\n  {\n    title: 'الإحصائيات',\n    description: 'تقارير وإحصائيات مفصلة',\n    icon: BarChart3,\n    href: '/statistics',\n    gradient: 'from-indigo-500 to-indigo-600',\n    roles: ['manager', 'supervisor', 'courier'],\n  },\n  {\n    title: 'الأرشيف',\n    description: 'أرشيف الطلبات المكتملة',\n    icon: Archive,\n    href: '/archive',\n    gradient: 'from-gray-500 to-gray-600',\n    roles: ['manager', 'supervisor', 'courier'],\n  },\n  {\n    title: 'إدارة الموظفين',\n    description: 'إضافة وإدارة المستخدمين',\n    icon: Users,\n    href: '/users',\n    gradient: 'from-teal-500 to-teal-600',\n    roles: ['manager', 'supervisor'],\n  },\n  {\n    title: 'استيراد وتصدير',\n    description: 'استيراد وتصدير البيانات',\n    icon: Download,\n    href: '/import-export',\n    gradient: 'from-pink-500 to-pink-600',\n    roles: ['manager'],\n  },\n  {\n    title: 'الإشعارات',\n    description: 'إدارة الإشعارات والتنبيهات',\n    icon: Bell,\n    href: '/notifications',\n    gradient: 'from-yellow-500 to-yellow-600',\n    roles: ['manager', 'supervisor', 'courier'],\n  },\n  {\n    title: 'الإعدادات',\n    description: 'إعدادات النظام والتطبيق',\n    icon: Settings,\n    href: '/settings',\n    gradient: 'from-slate-500 to-slate-600',\n    roles: ['manager', 'supervisor', 'courier'],\n  },\n]\n\nexport default function HomePage() {\n  const { user, isLoading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!isLoading && !user) {\n      router.push('/login')\n    }\n  }, [user, isLoading, router])\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">جاري التحميل...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null\n  }\n\n  // فلترة البطاقات حسب دور المستخدم\n  const filteredCards = dashboardCards.filter(card =>\n    card.roles.includes(user.role)\n  )\n\n  // حساب الإحصائيات\n  const stats = calculateStatistics(user.role === 'courier' ? user.id : undefined)\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n\n      <div className=\"flex\">\n        <Navigation />\n\n        <main className=\"flex-1 p-6\">\n          {/* ترحيب */}\n          <div className=\"mb-8\">\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n              مرحباً، {user.name}\n            </h1>\n            <p className=\"text-gray-600\">\n              {user.role === 'manager' && 'لوحة تحكم المدير'}\n              {user.role === 'supervisor' && 'لوحة تحكم المتابع'}\n              {user.role === 'courier' && 'لوحة تحكم المندوب'}\n            </p>\n          </div>\n\n          {/* إحصائيات سريعة */}\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"p-2 bg-blue-100 rounded-lg\">\n                    <Package className=\"h-6 w-6 text-blue-600\" />\n                  </div>\n                  <div className=\"mr-4\">\n                    <p className=\"text-sm font-medium text-gray-600\">إجمالي الطلبات</p>\n                    <p className=\"text-2xl font-bold text-gray-900\">{stats.totalOrders}</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"p-2 bg-green-100 rounded-lg\">\n                    <Package className=\"h-6 w-6 text-green-600\" />\n                  </div>\n                  <div className=\"mr-4\">\n                    <p className=\"text-sm font-medium text-gray-600\">تم التسليم</p>\n                    <p className=\"text-2xl font-bold text-gray-900\">{stats.deliveredOrders}</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"p-2 bg-orange-100 rounded-lg\">\n                    <Package className=\"h-6 w-6 text-orange-600\" />\n                  </div>\n                  <div className=\"mr-4\">\n                    <p className=\"text-sm font-medium text-gray-600\">قيد التوصيل</p>\n                    <p className=\"text-2xl font-bold text-gray-900\">{stats.outForDeliveryOrders}</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center\">\n                  <div className=\"p-2 bg-purple-100 rounded-lg\">\n                    <Calculator className=\"h-6 w-6 text-purple-600\" />\n                  </div>\n                  <div className=\"mr-4\">\n                    <p className=\"text-sm font-medium text-gray-600\">إجمالي الإيرادات</p>\n                    <p className=\"text-2xl font-bold text-gray-900\">{stats.totalRevenue.toLocaleString()} د.ع</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* بطاقات الأقسام */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {filteredCards.map((card) => {\n              const Icon = card.icon\n              return (\n                <Card\n                  key={card.title}\n                  className=\"cursor-pointer hover:shadow-lg transition-shadow duration-200\"\n                  onClick={() => router.push(card.href)}\n                >\n                  <CardHeader>\n                    <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${card.gradient} flex items-center justify-center mb-4`}>\n                      <Icon className=\"h-6 w-6 text-white\" />\n                    </div>\n                    <CardTitle className=\"text-xl\">{card.title}</CardTitle>\n                    <CardDescription>{card.description}</CardDescription>\n                  </CardHeader>\n                </Card>\n              )\n            })}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AARA;;;;;;;;;;AA+BA,MAAM,iBAAkC;IACtC;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;YAAC;YAAW;YAAc;SAAU;IAC7C;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;YAAC;SAAU;IACpB;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;YAAC;SAAU;IACpB;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;YAAC;SAAU;IACpB;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;YAAC;YAAW;YAAc;SAAU;IAC7C;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;YAAC;YAAW;YAAc;SAAU;IAC7C;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;YAAC;YAAW;SAAa;IAClC;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;YAAC;SAAU;IACpB;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;YAAC;YAAW;YAAc;SAAU;IAC7C;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;YAAC;YAAW;YAAc;SAAU;IAC7C;CACD;AAEc,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,CAAC,MAAM;YACvB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAW;KAAO;IAE5B,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,kCAAkC;IAClC,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAA,OAC1C,KAAK,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI;IAG/B,kBAAkB;IAClB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,IAAI,KAAK,YAAY,KAAK,EAAE,GAAG;IAEtE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,aAAU;;;;;kCAEX,8OAAC;wBAAK,WAAU;;0CAEd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAwC;4CAC3C,KAAK,IAAI;;;;;;;kDAEpB,8OAAC;wCAAE,WAAU;;4CACV,KAAK,IAAI,KAAK,aAAa;4CAC3B,KAAK,IAAI,KAAK,gBAAgB;4CAC9B,KAAK,IAAI,KAAK,aAAa;;;;;;;;;;;;;0CAKhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gIAAA,CAAA,OAAI;kDACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAQ,WAAU;;;;;;;;;;;kEAErB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAoC,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAM1E,8OAAC,gIAAA,CAAA,OAAI;kDACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAQ,WAAU;;;;;;;;;;;kEAErB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAoC,MAAM,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAM9E,8OAAC,gIAAA,CAAA,OAAI;kDACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAQ,WAAU;;;;;;;;;;;kEAErB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAoC,MAAM,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMnF,8OAAC,gIAAA,CAAA,OAAI;kDACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAW,WAAU;;;;;;;;;;;kEAExB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;;oEAAoC,MAAM,YAAY,CAAC,cAAc;oEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ/F,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC;oCAClB,MAAM,OAAO,KAAK,IAAI;oCACtB,qBACE,8OAAC,gIAAA,CAAA,OAAI;wCAEH,WAAU;wCACV,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,IAAI;kDAEpC,cAAA,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC;oDAAI,WAAW,CAAC,sCAAsC,EAAE,KAAK,QAAQ,CAAC,sCAAsC,CAAC;8DAC5G,cAAA,8OAAC;wDAAK,WAAU;;;;;;;;;;;8DAElB,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAW,KAAK,KAAK;;;;;;8DAC1C,8OAAC,gIAAA,CAAA,kBAAe;8DAAE,KAAK,WAAW;;;;;;;;;;;;uCAT/B,KAAK,KAAK;;;;;gCAarB;;;;;;;;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}]}